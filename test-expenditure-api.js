// Test script to verify expenditure API fixes
const testExpenditureAPI = async () => {
  const testData = {
    // Basic Information (Expenditure model fields)
    title: "Test Office Supplies Purchase",
    description: "Testing the new expenditure API with office supplies",
    category: "OTHER",
    subcategory: "OTHER",

    // Financial Information
    amount: 1500.00,
    currency: "MWK",
    exchangeRate: 1,
    amountInBaseCurrency: 1500.00,

    // Dates
    expenditureDate: new Date().toISOString(),

    // Status and Priority
    status: "DRAFT",
    priority: "MEDIUM",

    // Requestor Information (will be set by API)
    department: "administration",
    costCenter: "admin-001",

    // Vendor Information
    vendor: {
      vendorName: "Test Vendor Ltd",
      vendorEmail: "<EMAIL>",
      vendorPhone: "+************"
    },

    // Budget and Allocation
    budgetAllocations: [],

    // Payment and Processing
    paymentMethod: "BANK_TRANSFER",

    // Additional fields
    fiscalYear: "2025-2026",
    appliedToBudget: false,
    tags: ["test", "office-supplies"],
    notes: [{ note: "This is a test expenditure", createdAt: new Date() }],
    isUrgent: false,
    requiresReceipt: true,
    isCapitalExpenditure: false
  };

  try {
    console.log('Testing NEW expenditure API with data:', testData);

    const response = await fetch('http://localhost:3001/api/accounting/expenditure', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testData)
    });

    const result = await response.json();

    if (response.ok) {
      console.log('✅ SUCCESS: Expenditure created successfully');
      console.log('Response:', result);
    } else {
      console.log('❌ ERROR: Failed to create expenditure');
      console.log('Status:', response.status);
      console.log('Error:', result);
    }
  } catch (error) {
    console.log('❌ NETWORK ERROR:', error.message);
  }
};

// Run the test
testExpenditureAPI();
