// Test script to verify expenditure API fixes
const testExpenditureAPI = async () => {
  const testData = {
    date: new Date().toISOString(),
    category: "Test Office Supplies",
    amount: 1500.00,
    reference: "TEST-REF-001",
    fiscalYear: "2025-2026",
    status: "draft",
    paymentMethod: "bank_transfer",
    department: "administration",
    // These fields should now handle empty strings properly
    budget: "",
    budgetCategory: "",
    budgetSubcategory: "",
    vendor: "",
    costCenter: "",
    subcategory: "",
    description: "",
    notes: "",
    appliedToBudget: false
  };

  try {
    console.log('Testing expenditure API with data:', testData);
    
    const response = await fetch('http://localhost:3001/api/accounting/expense', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testData)
    });

    const result = await response.json();
    
    if (response.ok) {
      console.log('✅ SUCCESS: Expenditure created successfully');
      console.log('Response:', result);
    } else {
      console.log('❌ ERROR: Failed to create expenditure');
      console.log('Status:', response.status);
      console.log('Error:', result);
    }
  } catch (error) {
    console.log('❌ NETWORK ERROR:', error.message);
  }
};

// Run the test
testExpenditureAPI();
