// Test script to verify expenditure API fixes
const testExpenditureAPI = async () => {
  const testData = {
    // Required basic fields only
    title: "Test Office Supplies Purchase",
    description: "Testing the simplified expenditure API",
    amount: 1500.00,
    expenditureDate: new Date().toISOString(),

    // Optional fields
    department: "administration",
    costCenter: "admin-001",
    fiscalYear: "2025-2026",
    appliedToBudget: false,
    tags: ["test", "office-supplies"],

    // Vendor Information (simplified)
    vendor: {
      vendorName: "Test Vendor Ltd"
    }
  };

  try {
    console.log('Testing NEW expenditure API with data:', testData);

    const response = await fetch('http://localhost:3001/api/accounting/expenditure', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testData)
    });

    const result = await response.json();

    if (response.ok) {
      console.log('✅ SUCCESS: Expenditure created successfully');
      console.log('Response:', result);
    } else {
      console.log('❌ ERROR: Failed to create expenditure');
      console.log('Status:', response.status);
      console.log('Error:', result);
    }
  } catch (error) {
    console.log('❌ NETWORK ERROR:', error.message);
  }
};

// Run the test
testExpenditureAPI();
