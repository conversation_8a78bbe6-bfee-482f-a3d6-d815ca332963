// app/api/accounting/expenditure/drafts/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/database';
import { logger } from '@/lib/utils/logger';
import Expense from '@/models/accounting/Expense';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';

export const runtime = 'nodejs';

export async function GET(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Check permissions - only specific roles can view drafts
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT,
      UserRole.HR_MANAGER
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions to view draft expenditures' },
        { status: 403 }
      );
    }

    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const status = searchParams.get('status') || 'draft';
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const skip = (page - 1) * limit;

    // Build filter for draft expenditures
    const filter: Record<string, any> = {};

    // Filter by status (default to draft, but allow other pending statuses)
    // Handle both uppercase and lowercase status values for compatibility
    const allowedStatuses = ['draft', 'submitted', 'pending_approval', 'rejected', 'on_hold'];
    const allowedStatusesUpper = ['DRAFT', 'SUBMITTED', 'PENDING_APPROVAL', 'REJECTED', 'ON_HOLD'];

    if (allowedStatuses.includes(status.toLowerCase())) {
      // Support both uppercase and lowercase status values
      filter.status = { $in: [status.toLowerCase(), status.toUpperCase()] };
    } else {
      filter.status = { $in: [...allowedStatuses, ...allowedStatusesUpper] };
    }

    // Get total count for pagination
    const totalCount = await Expense.countDocuments(filter);
    const totalPages = Math.ceil(totalCount / limit);

    // Fetch draft expenditure records
    const draftExpenditures = await Expense.find(filter)
      .sort({ createdAt: -1 }) // Most recent first
      .skip(skip)
      .limit(limit)
      .populate('createdBy', 'firstName lastName email')
      .populate('updatedBy', 'firstName lastName email')
      .populate('budget', 'name fiscalYear')
      .populate('budgetCategory', 'name type')
      .lean();

    // Get summary statistics
    const summaryStats = await Expense.aggregate([
      {
        $match: {
          status: { $in: [...allowedStatuses, ...allowedStatusesUpper] }
        }
      },
      {
        $group: {
          _id: { $toLower: '$status' }, // Normalize to lowercase for grouping
          count: { $sum: 1 },
          totalAmount: { $sum: '$amount' }
        }
      }
    ]);

    // Format summary statistics
    const summary = {
      draft: { count: 0, totalAmount: 0 },
      submitted: { count: 0, totalAmount: 0 },
      pending_approval: { count: 0, totalAmount: 0 },
      rejected: { count: 0, totalAmount: 0 },
      on_hold: { count: 0, totalAmount: 0 }
    };

    summaryStats.forEach(stat => {
      if (summary[stat._id as keyof typeof summary]) {
        summary[stat._id as keyof typeof summary] = {
          count: stat.count,
          totalAmount: stat.totalAmount
        };
      }
    });

    // Calculate totals
    const totalDraftAmount = Object.values(summary).reduce((sum, stat) => sum + stat.totalAmount, 0);
    const totalDraftCount = Object.values(summary).reduce((sum, stat) => sum + stat.count, 0);

    return NextResponse.json({
      draftExpenditures,
      summary,
      totals: {
        count: totalDraftCount,
        amount: totalDraftAmount
      },
      pagination: {
        totalCount,
        totalPages,
        currentPage: page,
        limit,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1
      },
      filters: {
        statuses: [
          { value: 'draft', label: 'Draft', count: summary.draft.count },
          { value: 'submitted', label: 'Submitted', count: summary.submitted.count },
          { value: 'pending_approval', label: 'Pending Approval', count: summary.pending_approval.count },
          { value: 'rejected', label: 'Rejected', count: summary.rejected.count },
          { value: 'on_hold', label: 'On Hold', count: summary.on_hold.count }
        ]
      }
    });

  } catch (error: unknown) {
    console.error('Error fetching draft expenditures:', error);
    logger.error('Error fetching draft expenditures', error);
    
    const errorMessage = error instanceof Error ? error.message : 'Internal Server Error';
    
    return NextResponse.json(
      { error: errorMessage },
      { status: 500 }
    );
  }
}

// POST method for bulk status updates
export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT,
      UserRole.HR_MANAGER
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions for bulk operations' },
        { status: 403 }
      );
    }

    // Get request body
    const data = await req.json();
    const { expenditureIds, action, notes } = data;

    if (!expenditureIds || !Array.isArray(expenditureIds) || expenditureIds.length === 0) {
      return NextResponse.json(
        { error: 'Expenditure IDs are required for bulk operations' },
        { status: 400 }
      );
    }

    if (!action || !['approve', 'reject', 'pay', 'cancel', 'submit'].includes(action)) {
      return NextResponse.json(
        { error: 'Valid action is required (approve, reject, pay, cancel, submit)' },
        { status: 400 }
      );
    }

    // Map actions to statuses
    const statusMap: Record<string, string> = {
      'approve': 'approved',
      'reject': 'rejected',
      'pay': 'paid',
      'cancel': 'cancelled',
      'submit': 'submitted'
    };

    const newStatus = statusMap[action];
    const results = [];

    // Process each expenditure record
    for (const expenditureId of expenditureIds) {
      try {
        const expenditure = await Expense.findById(expenditureId);
        if (!expenditure) {
          results.push({ expenditureId, success: false, error: 'Expenditure not found' });
          continue;
        }

        // Update status
        const previousStatus = expenditure.status;
        expenditure.status = newStatus;
        expenditure.updatedBy = user.id;
        expenditure.updatedAt = new Date();

        // Set specific timestamps
        switch (newStatus) {
          case 'submitted':
            expenditure.submittedAt = new Date();
            expenditure.submittedBy = user.id;
            break;
          case 'approved':
            expenditure.approvedAt = new Date();
            expenditure.approvedBy = user.id;
            break;
          case 'paid':
            expenditure.paidAt = new Date();
            expenditure.paidBy = user.id;
            break;
          case 'rejected':
            expenditure.rejectedAt = new Date();
            expenditure.rejectedBy = user.id;
            expenditure.rejectionReason = notes || 'Bulk rejection';
            break;
          case 'cancelled':
            expenditure.cancelledAt = new Date();
            expenditure.cancelledBy = user.id;
            break;
        }

        await expenditure.save();
        results.push({ expenditureId, success: true, previousStatus, newStatus });

      } catch (error) {
        results.push({ 
          expenditureId, 
          success: false, 
          error: error instanceof Error ? error.message : 'Unknown error' 
        });
      }
    }

    // Count successful operations
    const successCount = results.filter(r => r.success).length;
    const failureCount = results.filter(r => !r.success).length;

    logger.info('Bulk expenditure status update completed', {
      action,
      totalRecords: expenditureIds.length,
      successCount,
      failureCount,
      updatedBy: user.id
    });

    return NextResponse.json({
      success: true,
      message: `Bulk ${action} completed: ${successCount} successful, ${failureCount} failed`,
      results,
      summary: {
        total: expenditureIds.length,
        successful: successCount,
        failed: failureCount
      }
    });

  } catch (error: unknown) {
    console.error('Error in bulk expenditure status update:', error);
    logger.error('Error in bulk expenditure status update', error);
    
    const errorMessage = error instanceof Error ? error.message : 'Internal Server Error';
    
    return NextResponse.json(
      { error: errorMessage },
      { status: 500 }
    );
  }
}
