'use client';

import React, { useState } from 'react';
import { useToast } from '@/hooks/use-toast';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import {
  Download,
  FileSpreadsheet,
  FileText,
  Loader2,
  CheckCircle,
  Calendar,
  Filter,
  Settings
} from 'lucide-react';
import { DateRange } from 'react-day-picker';
import { DateRangePicker } from '@/components/accounting/shared/date-range-picker';

interface ExpenditureExportProps {
  fiscalYear?: string;
  budgetId?: string;
  trigger?: React.ReactNode;
}

interface ExportOptions {
  format: 'excel' | 'csv' | 'pdf';
  dateRange?: DateRange;
  fiscalYear?: string;
  status?: string;
  category?: string;
  department?: string;
  includeFields: {
    basic: boolean;
    budget: boolean;
    status: boolean;
    financial: boolean;
    audit: boolean;
  };
  groupBy?: 'none' | 'category' | 'department' | 'status' | 'month';
  includeSummary: boolean;
  includeCharts: boolean;
}

const DEFAULT_EXPORT_OPTIONS: ExportOptions = {
  format: 'excel',
  includeFields: {
    basic: true,
    budget: true,
    status: true,
    financial: true,
    audit: false,
  },
  groupBy: 'none',
  includeSummary: true,
  includeCharts: false,
};

export function ExpenditureExport({
  fiscalYear,
  budgetId,
  trigger
}: ExpenditureExportProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [isExporting, setIsExporting] = useState(false);
  const [exportProgress, setExportProgress] = useState(0);
  const [exportOptions, setExportOptions] = useState<ExportOptions>(DEFAULT_EXPORT_OPTIONS);
  const { toast } = useToast();

  const handleExport = async () => {
    setIsExporting(true);
    setExportProgress(0);

    try {
      // Simulate progress
      const progressInterval = setInterval(() => {
        setExportProgress(prev => {
          const newProgress = prev + 10;
          return newProgress >= 90 ? 90 : newProgress;
        });
      }, 200);

      // Prepare export parameters
      const exportParams = new URLSearchParams();
      
      if (exportOptions.fiscalYear) {
        exportParams.append('fiscalYear', exportOptions.fiscalYear);
      }
      if (budgetId) {
        exportParams.append('budgetId', budgetId);
      }
      if (exportOptions.status) {
        exportParams.append('status', exportOptions.status);
      }
      if (exportOptions.category) {
        exportParams.append('category', exportOptions.category);
      }
      if (exportOptions.department) {
        exportParams.append('department', exportOptions.department);
      }
      if (exportOptions.dateRange?.from) {
        exportParams.append('startDate', exportOptions.dateRange.from.toISOString());
      }
      if (exportOptions.dateRange?.to) {
        exportParams.append('endDate', exportOptions.dateRange.to.toISOString());
      }

      // Add field selections
      Object.entries(exportOptions.includeFields).forEach(([key, value]) => {
        if (value) {
          exportParams.append('includeFields', key);
        }
      });

      if (exportOptions.groupBy && exportOptions.groupBy !== 'none') {
        exportParams.append('groupBy', exportOptions.groupBy);
      }
      if (exportOptions.includeSummary) {
        exportParams.append('includeSummary', 'true');
      }
      if (exportOptions.includeCharts) {
        exportParams.append('includeCharts', 'true');
      }

      // Determine API endpoint based on format
      const endpoint = `/api/accounting/expense/export/${exportOptions.format}?${exportParams.toString()}`;

      // Make export request
      const response = await fetch(endpoint, {
        method: 'GET',
      });

      clearInterval(progressInterval);
      setExportProgress(100);

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `Export failed with status ${response.status}`);
      }

      // Download the file
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      
      // Set filename based on format
      const timestamp = new Date().toISOString().split('T')[0];
      const filename = `expenditures_${timestamp}.${exportOptions.format === 'excel' ? 'xlsx' : exportOptions.format}`;
      link.download = filename;
      
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      toast({
        title: "Export Successful",
        description: `Expenditure data exported successfully as ${exportOptions.format.toUpperCase()}`,
      });

      setIsOpen(false);
    } catch (error) {
      console.error('Export error:', error);
      toast({
        title: "Export Failed",
        description: error instanceof Error ? error.message : 'Failed to export expenditure data',
        variant: "destructive",
      });
      setExportProgress(0);
    } finally {
      setIsExporting(false);
    }
  };

  const updateExportOption = <K extends keyof ExportOptions>(
    key: K,
    value: ExportOptions[K]
  ) => {
    setExportOptions(prev => ({ ...prev, [key]: value }));
  };

  const updateIncludeField = (field: keyof ExportOptions['includeFields'], value: boolean) => {
    setExportOptions(prev => ({
      ...prev,
      includeFields: {
        ...prev.includeFields,
        [field]: value
      }
    }));
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        {trigger || (
          <Button variant="outline" size="sm">
            <Download className="mr-2 h-4 w-4" />
            Export
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="sm:max-w-[600px] max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Download className="h-5 w-5" />
            Export Expenditure Data
          </DialogTitle>
          <DialogDescription>
            Configure export options and download expenditure data in your preferred format
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Export Format */}
          <div className="space-y-3">
            <Label className="text-base font-medium">Export Format</Label>
            <div className="grid grid-cols-3 gap-3">
              {[
                { value: 'excel', label: 'Excel', icon: FileSpreadsheet, description: 'Full featured with charts' },
                { value: 'csv', label: 'CSV', icon: FileText, description: 'Simple data format' },
                { value: 'pdf', label: 'PDF', icon: FileText, description: 'Formatted report' }
              ].map(format => (
                <Card
                  key={format.value}
                  className={`cursor-pointer transition-colors ${
                    exportOptions.format === format.value ? 'ring-2 ring-primary' : ''
                  }`}
                  onClick={() => updateExportOption('format', format.value as any)}
                >
                  <CardContent className="p-4 text-center">
                    <format.icon className="h-8 w-8 mx-auto mb-2" />
                    <div className="font-medium">{format.label}</div>
                    <div className="text-xs text-muted-foreground">{format.description}</div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>

          <Separator />

          {/* Filters */}
          <div className="space-y-4">
            <Label className="text-base font-medium flex items-center gap-2">
              <Filter className="h-4 w-4" />
              Filters
            </Label>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Fiscal Year */}
              <div className="space-y-2">
                <Label>Fiscal Year</Label>
                <Select
                  value={exportOptions.fiscalYear || ''}
                  onValueChange={(value) => updateExportOption('fiscalYear', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="All fiscal years" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">All fiscal years</SelectItem>
                    <SelectItem value="2025-2026">2025-2026</SelectItem>
                    <SelectItem value="2024-2025">2024-2025</SelectItem>
                    <SelectItem value="2023-2024">2023-2024</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Status */}
              <div className="space-y-2">
                <Label>Status</Label>
                <Select
                  value={exportOptions.status || ''}
                  onValueChange={(value) => updateExportOption('status', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="All statuses" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">All statuses</SelectItem>
                    <SelectItem value="draft">Draft</SelectItem>
                    <SelectItem value="pending_approval">Pending Approval</SelectItem>
                    <SelectItem value="approved">Approved</SelectItem>
                    <SelectItem value="paid">Paid</SelectItem>
                    <SelectItem value="rejected">Rejected</SelectItem>
                    <SelectItem value="cancelled">Cancelled</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Category */}
              <div className="space-y-2">
                <Label>Category</Label>
                <Select
                  value={exportOptions.category || ''}
                  onValueChange={(value) => updateExportOption('category', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="All categories" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">All categories</SelectItem>
                    <SelectItem value="office_supplies">Office Supplies</SelectItem>
                    <SelectItem value="travel_transport">Travel & Transport</SelectItem>
                    <SelectItem value="utilities">Utilities</SelectItem>
                    <SelectItem value="professional_services">Professional Services</SelectItem>
                    <SelectItem value="equipment">Equipment</SelectItem>
                    <SelectItem value="maintenance">Maintenance</SelectItem>
                    <SelectItem value="training">Training</SelectItem>
                    <SelectItem value="communications">Communications</SelectItem>
                    <SelectItem value="insurance">Insurance</SelectItem>
                    <SelectItem value="personnel">Personnel</SelectItem>
                    <SelectItem value="administrative">Administrative</SelectItem>
                    <SelectItem value="other">Other</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Department */}
              <div className="space-y-2">
                <Label>Department</Label>
                <Select
                  value={exportOptions.department || ''}
                  onValueChange={(value) => updateExportOption('department', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="All departments" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">All departments</SelectItem>
                    <SelectItem value="administration">Administration</SelectItem>
                    <SelectItem value="finance">Finance</SelectItem>
                    <SelectItem value="hr">Human Resources</SelectItem>
                    <SelectItem value="it">Information Technology</SelectItem>
                    <SelectItem value="operations">Operations</SelectItem>
                    <SelectItem value="general">General</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Date Range */}
            <div className="space-y-2">
              <Label className="flex items-center gap-2">
                <Calendar className="h-4 w-4" />
                Date Range (Optional)
              </Label>
              <DateRangePicker
                value={exportOptions.dateRange}
                onChange={(range) => updateExportOption('dateRange', range)}
                placeholder="Select date range"
              />
            </div>
          </div>

          <Separator />

          {/* Include Fields */}
          <div className="space-y-4">
            <Label className="text-base font-medium flex items-center gap-2">
              <Settings className="h-4 w-4" />
              Include Fields
            </Label>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {[
                { key: 'basic', label: 'Basic Information', description: 'Date, reference, amount, category' },
                { key: 'budget', label: 'Budget Information', description: 'Budget, category, utilization' },
                { key: 'status', label: 'Status Information', description: 'Status, approval dates, workflow' },
                { key: 'financial', label: 'Financial Details', description: 'Payment method, vendor, cost center' },
                { key: 'audit', label: 'Audit Trail', description: 'Created by, modified by, timestamps' }
              ].map(field => (
                <div key={field.key} className="flex items-start space-x-3">
                  <Checkbox
                    id={field.key}
                    checked={exportOptions.includeFields[field.key as keyof ExportOptions['includeFields']]}
                    onCheckedChange={(checked) => 
                      updateIncludeField(field.key as keyof ExportOptions['includeFields'], !!checked)
                    }
                  />
                  <div className="grid gap-1.5 leading-none">
                    <Label htmlFor={field.key} className="text-sm font-medium">
                      {field.label}
                    </Label>
                    <p className="text-xs text-muted-foreground">
                      {field.description}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>

          <Separator />

          {/* Export Options */}
          <div className="space-y-4">
            <Label className="text-base font-medium">Export Options</Label>
            
            <div className="space-y-3">
              {/* Group By */}
              <div className="space-y-2">
                <Label>Group By</Label>
                <Select
                  value={exportOptions.groupBy || 'none'}
                  onValueChange={(value) => updateExportOption('groupBy', value as any)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="none">No grouping</SelectItem>
                    <SelectItem value="category">Category</SelectItem>
                    <SelectItem value="department">Department</SelectItem>
                    <SelectItem value="status">Status</SelectItem>
                    <SelectItem value="month">Month</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Additional Options */}
              <div className="space-y-3">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="includeSummary"
                    checked={exportOptions.includeSummary}
                    onCheckedChange={(checked) => updateExportOption('includeSummary', !!checked)}
                  />
                  <Label htmlFor="includeSummary">Include summary statistics</Label>
                </div>

                {exportOptions.format === 'excel' && (
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="includeCharts"
                      checked={exportOptions.includeCharts}
                      onCheckedChange={(checked) => updateExportOption('includeCharts', !!checked)}
                    />
                    <Label htmlFor="includeCharts">Include charts and graphs</Label>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Export Progress */}
          {isExporting && (
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Exporting...</span>
                <span className="text-sm text-muted-foreground">{exportProgress}%</span>
              </div>
              <Progress value={exportProgress} className="w-full" />
            </div>
          )}
        </div>

        <DialogFooter>
          <Button
            type="button"
            variant="outline"
            onClick={() => setIsOpen(false)}
            disabled={isExporting}
          >
            Cancel
          </Button>
          <Button
            type="button"
            onClick={handleExport}
            disabled={isExporting}
          >
            {isExporting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Exporting...
              </>
            ) : (
              <>
                <Download className="mr-2 h-4 w-4" />
                Export Data
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
