'use client';

import React, { useState, useCallback, useMemo } from 'react';
import { useToast } from '@/hooks/use-toast';
import { Button } from '@/components/ui/button';
import { useIncomeDataPrefetcher } from '../income/income-data-prefetcher';
import { Skeleton } from '@/components/ui/skeleton';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Calendar } from '@/components/ui/calendar';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Loader2, CalendarIcon, Receipt } from 'lucide-react';
import { format } from 'date-fns';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Label } from '@/components/ui/label';

// Expenditure form data interface
interface ExpenditureFormData {
  date: Date;
  category: string;
  subcategory?: string;
  amount: string;
  reference: string;
  description?: string;
  fiscalYear: string;
  status: string;
  paymentMethod?: string;
  vendor?: string;
  department?: string;
  budget: string;
  budgetCategory: string;
  notes?: string;
}

interface ExpenditureFormProps {
  expenditure?: any;
  onSubmit: (data: any) => Promise<void>;
  onCancel: () => void;
  isLoading?: boolean;
}

// Static data for expenditure form
const STATIC_EXPENDITURE_DATA = {
  categories: [
    { value: 'operational', label: 'Operational' },
    { value: 'capital', label: 'Capital' },
    { value: 'personnel', label: 'Personnel' },
    { value: 'administrative', label: 'Administrative' },
    { value: 'travel', label: 'Travel' },
    { value: 'utilities', label: 'Utilities' },
    { value: 'maintenance', label: 'Maintenance' },
    { value: 'supplies', label: 'Supplies' },
    { value: 'professional_services', label: 'Professional Services' },
    { value: 'training', label: 'Training' },
    { value: 'marketing', label: 'Marketing' },
    { value: 'technology', label: 'Technology' },
    { value: 'insurance', label: 'Insurance' },
    { value: 'legal', label: 'Legal' },
    { value: 'other', label: 'Other' },
  ],
  fiscalYears: [
    { value: '2025-2026', label: '2025-2026 (Current)' },
    { value: '2024-2025', label: '2024-2025' },
    { value: '2023-2024', label: '2023-2024' },
  ],
  statusOptions: [
    { value: 'draft', label: 'Draft' },
    { value: 'submitted', label: 'Submitted' },
    { value: 'pending_approval', label: 'Pending Approval' },
    { value: 'approved', label: 'Approved' },
    { value: 'paid', label: 'Paid' },
  ],
  paymentMethods: [
    { value: 'cash', label: 'Cash' },
    { value: 'bank_transfer', label: 'Bank Transfer' },
    { value: 'cheque', label: 'Cheque' },
    { value: 'credit_card', label: 'Credit Card' },
    { value: 'mobile_money', label: 'Mobile Money' },
    { value: 'petty_cash', label: 'Petty Cash' },
  ],
  departments: [
    { value: 'general', label: 'General' },
    { value: 'administration', label: 'Administration' },
    { value: 'finance', label: 'Finance' },
    { value: 'hr', label: 'Human Resources' },
    { value: 'it', label: 'Information Technology' },
    { value: 'operations', label: 'Operations' },
  ],
};

export function ExpenditureForm({
  expenditure,
  onSubmit,
  onCancel,
  isLoading = false
}: ExpenditureFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Get prefetched budget data (reuse income data prefetcher for budgets)
  const { isReady, isLoading: isDataLoading, budgets, budgetCategories } = useIncomeDataPrefetcher();

  // Initialize form data
  const [formData, setFormData] = useState<ExpenditureFormData>(() => ({
    date: expenditure?.date ? new Date(expenditure.date) : new Date(),
    category: expenditure?.category || 'operational',
    subcategory: expenditure?.subcategory || '',
    amount: expenditure?.amount?.toString() || '',
    reference: expenditure?.reference || '',
    description: expenditure?.description || '',
    fiscalYear: expenditure?.fiscalYear || '2025-2026',
    status: expenditure?.status || 'draft',
    paymentMethod: expenditure?.paymentMethod || '',
    vendor: expenditure?.vendor || '',
    department: expenditure?.department || 'general',
    budget: expenditure?.budget || (budgets.length > 0 ? (budgets[0].id || budgets[0]._id) : ''),
    budgetCategory: expenditure?.budgetCategory || '',
    notes: expenditure?.notes || '',
  }));

  // Form validation errors
  const [errors, setErrors] = useState<Record<string, string>>({});
  
  const { toast } = useToast();

  // Update field function
  const updateField = useCallback((field: keyof ExpenditureFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error for this field immediately
    setErrors(prev => {
      if (prev[field]) {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      }
      return prev;
    });
  }, []);

  // Validate form
  const validateForm = useCallback((): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.date) newErrors.date = 'Date is required';
    if (!formData.category) newErrors.category = 'Category is required';
    if (!formData.amount || parseFloat(formData.amount) <= 0) newErrors.amount = 'Amount must be greater than 0';
    if (!formData.reference.trim()) newErrors.reference = 'Reference is required';
    if (!formData.fiscalYear) newErrors.fiscalYear = 'Fiscal year is required';
    if (!formData.budget) newErrors.budget = 'Budget is required';
    if (!formData.budgetCategory) newErrors.budgetCategory = 'Budget category is required';

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  }, [formData]);

  // Handle form submission
  const handleSubmit = useCallback(async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (isSubmitting) return;

    if (!validateForm()) {
      toast({
        title: 'Validation Error',
        description: 'Please fix the errors in the form',
        variant: 'destructive',
      });
      return;
    }

    try {
      setIsSubmitting(true);

      // Process form data
      const processedValues = {
        ...formData,
        amount: parseFloat(formData.amount),
        date: formData.date.toISOString(),
        appliedToBudget: true, // Always apply to budget
      };

      await onSubmit(processedValues);

      toast({
        title: expenditure ? 'Expenditure Updated' : 'Expenditure Created',
        description: expenditure ? 'The expenditure has been updated successfully' : 'The expenditure has been created successfully',
      });

    } catch (error: unknown) {
      console.error('Error saving expenditure:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'An unknown error occurred',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  }, [formData, isSubmitting, validateForm, onSubmit, expenditure, toast]);

  // Filter budget categories for expenditure (expense type)
  const expenseBudgetCategories = useMemo(() => {
    return budgetCategories.filter(category => 
      category.type === 'expense' || category.type === 'expenditure'
    );
  }, [budgetCategories]);

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle>{expenditure ? 'Edit Expenditure' : 'Record New Expenditure'}</CardTitle>
          <Badge variant="default" className="text-xs">
            <Receipt className="h-3 w-3 mr-1" />
            Budget Integrated
          </Badge>
        </div>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-medium">Basic Information</h3>
            </div>
            <Separator />
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Date Field */}
              <div className="space-y-2">
                <Label htmlFor="date">Date *</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className="w-full pl-3 text-left font-normal"
                    >
                      {formData.date ? (
                        format(formData.date, "PPP")
                      ) : (
                        <span>Pick a date</span>
                      )}
                      <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      mode="single"
                      selected={formData.date}
                      onSelect={(date) => date && updateField('date', date)}
                      disabled={(date) =>
                        date > new Date() || date < new Date("1900-01-01")
                      }
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
                {errors.date && <p className="text-sm text-destructive">{errors.date}</p>}
              </div>

              {/* Fiscal Year Field */}
              <div className="space-y-2">
                <Label htmlFor="fiscalYear">Fiscal Year *</Label>
                <Select
                  value={formData.fiscalYear}
                  onValueChange={(value) => updateField('fiscalYear', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select fiscal year" />
                  </SelectTrigger>
                  <SelectContent>
                    {STATIC_EXPENDITURE_DATA.fiscalYears.map(year => (
                      <SelectItem key={year.value} value={year.value}>
                        {year.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.fiscalYear && <p className="text-sm text-destructive">{errors.fiscalYear}</p>}
              </div>
            </div>

            {/* Category and Amount */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="category">Category *</Label>
                <Select
                  value={formData.category}
                  onValueChange={(value) => updateField('category', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent>
                    {STATIC_EXPENDITURE_DATA.categories.map(category => (
                      <SelectItem key={category.value} value={category.value}>
                        {category.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.category && <p className="text-sm text-destructive">{errors.category}</p>}
              </div>

              <div className="space-y-2">
                <Label htmlFor="amount">Amount (MWK) *</Label>
                <Input
                  id="amount"
                  type="number"
                  step="0.01"
                  placeholder="Enter amount"
                  value={formData.amount}
                  onChange={(e) => updateField('amount', e.target.value)}
                />
                {errors.amount && <p className="text-sm text-destructive">{errors.amount}</p>}
              </div>
            </div>

            {/* Reference and Vendor */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="reference">Reference *</Label>
                <Input
                  id="reference"
                  placeholder="Enter reference number"
                  value={formData.reference}
                  onChange={(e) => updateField('reference', e.target.value)}
                />
                {errors.reference && <p className="text-sm text-destructive">{errors.reference}</p>}
              </div>

              <div className="space-y-2">
                <Label htmlFor="vendor">Vendor</Label>
                <Input
                  id="vendor"
                  placeholder="Enter vendor name"
                  value={formData.vendor || ''}
                  onChange={(e) => updateField('vendor', e.target.value)}
                />
              </div>
            </div>
          </div>

          {/* Budget Integration */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Budget Integration</h3>
            <Separator />

            {!isReady || isDataLoading ? (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label>Budget *</Label>
                  <Skeleton className="h-10 w-full" />
                </div>
                <div className="space-y-2">
                  <Label>Budget Category *</Label>
                  <Skeleton className="h-10 w-full" />
                </div>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="budget">Budget *</Label>
                  <Select
                    value={formData.budget}
                    onValueChange={(value) => updateField('budget', value)}
                    disabled={!isReady}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select budget" />
                    </SelectTrigger>
                    <SelectContent>
                      {budgets.map(budget => (
                        <SelectItem key={budget.id || budget._id} value={budget.id || budget._id}>
                          {budget.name} ({budget.fiscalYear})
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {errors.budget && <p className="text-sm text-destructive">{errors.budget}</p>}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="budgetCategory">Budget Category *</Label>
                  <Select
                    value={formData.budgetCategory}
                    onValueChange={(value) => updateField('budgetCategory', value)}
                    disabled={!isReady}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select category" />
                    </SelectTrigger>
                    <SelectContent>
                      {expenseBudgetCategories.map(category => (
                        <SelectItem key={category.id || category._id} value={category.id || category._id}>
                          {category.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {errors.budgetCategory && <p className="text-sm text-destructive">{errors.budgetCategory}</p>}
                </div>
              </div>
            )}
          </div>

          {/* Description */}
          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              placeholder="Enter description (optional)"
              value={formData.description || ''}
              onChange={(e) => updateField('description', e.target.value)}
            />
          </div>
        </form>
      </CardContent>
      <CardFooter className="flex justify-end space-x-2">
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
          disabled={isSubmitting}
        >
          Cancel
        </Button>
        <Button
          type="submit"
          onClick={handleSubmit}
          disabled={!isReady || isDataLoading || isSubmitting || isLoading}
        >
          {!isReady || isDataLoading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Loading...
            </>
          ) : isSubmitting ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              {expenditure ? 'Updating...' : 'Creating...'}
            </>
          ) : (
            expenditure ? 'Update Expenditure' : 'Create Expenditure'
          )}
        </Button>
      </CardFooter>
    </Card>
  );
}
