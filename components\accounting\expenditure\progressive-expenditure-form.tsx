'use client';

import React, { useState, useCallback, useEffect } from 'react';
import { useToast } from '@/hooks/use-toast';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Calendar } from '@/components/ui/calendar';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Loader2, CalendarIcon, Zap, CheckCircle, Receipt } from 'lucide-react';
import { format } from 'date-fns';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Label } from '@/components/ui/label';
import { Skeleton } from '@/components/ui/skeleton';
import {
  Budget,
  BudgetCategory,
  ExpenditureFormData,
  BudgetDataState,
  ExpenditureFormErrors,
  ExpenditureApiResponse
} from '@/types/expenditure';

// Progressive form data interface (using the imported type)
interface ProgressiveExpenditureFormData extends ExpenditureFormData {
  // All fields are inherited from ExpenditureFormData
}

interface ProgressiveExpenditureFormProps {
  expenditure?: Partial<ExpenditureFormData>;
  onSubmit: (data: any) => Promise<void>; // Accept any structure for flexibility
  onCancel: () => void;
  isLoading?: boolean;
}

// Static data that loads instantly (no backend dependency)
const INSTANT_FORM_DATA = {
  expenseCategories: [
    { value: 'office_supplies', label: 'Office Supplies' },
    { value: 'travel_transport', label: 'Travel & Transport' },
    { value: 'utilities', label: 'Utilities' },
    { value: 'professional_services', label: 'Professional Services' },
    { value: 'equipment', label: 'Equipment' },
    { value: 'maintenance', label: 'Maintenance' },
    { value: 'training', label: 'Training & Development' },
    { value: 'communications', label: 'Communications' },
    { value: 'insurance', label: 'Insurance' },
    { value: 'personnel', label: 'Personnel' },
    { value: 'administrative', label: 'Administrative' },
    { value: 'other', label: 'Other' },
  ],
  fiscalYears: [
    { value: '2025-2026', label: '2025-2026 (Current)' },
    { value: '2024-2025', label: '2024-2025' },
    { value: '2023-2024', label: '2023-2024' },
  ],
  statusOptions: [
    { value: 'draft', label: 'Draft' },
    { value: 'pending_approval', label: 'Pending Approval' },
    { value: 'approved', label: 'Approved' },
    { value: 'paid', label: 'Paid' },
  ],
  paymentMethods: [
    { value: 'bank_transfer', label: 'Bank Transfer' },
    { value: 'check', label: 'Check' },
    { value: 'cash', label: 'Cash' },
    { value: 'credit_card', label: 'Credit Card' },
    { value: 'mobile_money', label: 'Mobile Money' },
  ],
  departments: [
    { value: 'administration', label: 'Administration' },
    { value: 'finance', label: 'Finance' },
    { value: 'hr', label: 'Human Resources' },
    { value: 'it', label: 'Information Technology' },
    { value: 'operations', label: 'Operations' },
    { value: 'general', label: 'General' },
  ],
};

// Progressive data loader for budget data (mirrors income pattern)
class ProgressiveDataLoader {
  private static instance: ProgressiveDataLoader;
  private budgets: Budget[] = [];
  private budgetCategories: BudgetCategory[] = [];
  private isLoading = false;
  private isLoaded = false;

  static getInstance(): ProgressiveDataLoader {
    if (!ProgressiveDataLoader.instance) {
      ProgressiveDataLoader.instance = new ProgressiveDataLoader();
    }
    return ProgressiveDataLoader.instance;
  }

  async loadBudgetData(): Promise<{ budgets: Budget[]; budgetCategories: BudgetCategory[] }> {
    if (this.isLoaded) {
      return { budgets: this.budgets, budgetCategories: this.budgetCategories };
    }

    if (this.isLoading) {
      // Wait for existing load to complete
      while (this.isLoading) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
      return { budgets: this.budgets, budgetCategories: this.budgetCategories };
    }

    this.isLoading = true;

    try {
      // Check cache first
      const cacheTimestamp = localStorage.getItem('expenditure-form-cache-timestamp');
      const cacheAge = cacheTimestamp ? Date.now() - parseInt(cacheTimestamp) : Infinity;
      const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

      if (cacheAge < CACHE_DURATION) {
        const cachedBudgets = localStorage.getItem('expenditure-form-budgets');

        if (cachedBudgets) {
          this.budgets = JSON.parse(cachedBudgets);
          this.budgetCategories = []; // Categories will be loaded dynamically
          this.isLoaded = true;
          return { budgets: this.budgets, budgetCategories: this.budgetCategories };
        }
      }

      // Fetch only budget data (categories will be loaded when budget is selected)
      const budgetsResponse = await fetch('/api/accounting/budget?status=active');
      const budgetsData = budgetsResponse.ok ? await budgetsResponse.json() : { budgets: [] };

      // Process and normalize data
      const processedBudgets = (budgetsData.budgets || []).map((budget: any) => ({
        id: budget._id || budget.id,
        name: budget.name,
        fiscalYear: budget.fiscalYear,
        status: budget.status,
        totalExpense: budget.totalExpense || 0,
        totalActualExpense: budget.totalActualExpense || 0
      }));

      // Cache the budget data
      localStorage.setItem('expenditure-form-budgets', JSON.stringify(processedBudgets));
      localStorage.setItem('expenditure-form-cache-timestamp', Date.now().toString());

      this.budgets = processedBudgets;
      this.budgetCategories = []; // Will be loaded dynamically
      this.isLoaded = true;

      return { budgets: this.budgets, budgetCategories: this.budgetCategories };
    } catch (error) {
      console.error('Error loading budget data:', error);
      return { budgets: [], budgetCategories: [] };
    } finally {
      this.isLoading = false;
    }
  }

  // New method to load categories for a specific budget (using same pattern as income form)
  async loadBudgetCategories(budgetId: string): Promise<any[]> {
    try {
      // Try the same API pattern as income form first
      const response = await fetch(`/api/accounting/budget/${budgetId}/categories?type=expense`);
      if (!response.ok) {
        console.warn(`Failed to fetch from /api/accounting/budget/${budgetId}/categories, trying alternative...`);

        // Fallback to the budget endpoint like income form does
        const budgetResponse = await fetch(`/api/accounting/budget/${budgetId}`);
        if (budgetResponse.ok) {
          const budget = await budgetResponse.json();
          const expenseCategories = budget.categories?.filter((cat: any) =>
            cat.type === 'expense' || cat.type === 'expenditure'
          ) || [];

          return expenseCategories.map((category: any) => ({
            id: category.id || category._id,
            _id: category._id,
            name: category.name,
            description: category.description,
            type: category.type,
            budgetId: budgetId,
            budgetedAmount: category.budgetedAmount || 0,
            actualAmount: category.actualAmount || 0,
            isActive: category.isActive !== false
          }));
        }

        throw new Error('Failed to fetch budget categories');
      }

      const data = await response.json();
      const categories = (data.categories || []).map((category: any) => ({
        id: category.id || category._id,
        _id: category._id,
        name: category.name,
        description: category.description,
        type: category.type,
        budgetId: category.budgetId || budgetId,
        budgetedAmount: category.budgetedAmount || 0,
        actualAmount: category.actualAmount || 0,
        isActive: category.isActive !== false
      }));

      console.log('Loaded budget categories:', categories);
      return categories;
    } catch (error) {
      console.error('Error loading budget categories:', error);
      return [];
    }
  }
}

export function ProgressiveExpenditureForm({
  expenditure,
  onSubmit,
  onCancel,
  isLoading = false
}: ProgressiveExpenditureFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [budgetDataState, setBudgetDataState] = useState<BudgetDataState>({
    budgets: [],
    budgetCategories: [],
    isLoading: false,
    isLoaded: false
  });

  // Initialize form data with instant defaults (no backend dependency)
  const [formData, setFormData] = useState<ProgressiveExpenditureFormData>(() => ({
    date: expenditure?.date ? new Date(expenditure.date) : new Date(),
    description: expenditure?.description || '',
    subcategory: expenditure?.subcategory || '',
    amount: expenditure?.amount?.toString() || '',
    reference: expenditure?.reference || '',
    fiscalYear: expenditure?.fiscalYear || '2025-2026',
    status: expenditure?.status || 'draft',
    paymentMethod: expenditure?.paymentMethod || 'bank_transfer',
    vendor: expenditure?.vendor || '',
    department: expenditure?.department || 'general',
    costCenter: expenditure?.costCenter || '',
    budget: expenditure?.budget || '',
    budgetCategory: expenditure?.budgetCategory || '',
    budgetSubcategory: expenditure?.budgetSubcategory || '',
    appliedToBudget: expenditure?.appliedToBudget ?? true,
    notes: expenditure?.notes || '',
  }));

  const [errors, setErrors] = useState<Record<string, string>>({});
  const { toast } = useToast();

  // Progressive data loader instance
  const dataLoader = ProgressiveDataLoader.getInstance();

  const updateField = (field: keyof ProgressiveExpenditureFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error for this field
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  // Handle budget change and load categories
  const handleBudgetChange = useCallback(async (budgetId: string) => {
    console.log('Budget changed to:', budgetId);
    updateField('budget', budgetId);
    updateField('budgetCategory', ''); // Reset category when budget changes

    if (budgetId) {
      try {
        setBudgetDataState(prev => ({ ...prev, isLoading: true }));
        console.log('Loading categories for budget:', budgetId);
        const categories = await dataLoader.loadBudgetCategories(budgetId);
        console.log('Loaded categories:', categories);
        setBudgetDataState(prev => ({
          ...prev,
          budgetCategories: categories,
          isLoading: false
        }));
      } catch (error) {
        console.error('Error loading budget categories:', error);
        setBudgetDataState(prev => ({
          ...prev,
          budgetCategories: [],
          isLoading: false
        }));
      }
    } else {
      setBudgetDataState(prev => ({
        ...prev,
        budgetCategories: []
      }));
    }
  }, [updateField]);

  // Load budget data when budget dropdown is opened
  const handleBudgetDropdownOpen = useCallback(async () => {
    if (budgetDataState.isLoaded || budgetDataState.isLoading) return;

    setBudgetDataState(prev => ({ ...prev, isLoading: true }));

    try {
      const { budgets } = await dataLoader.loadBudgetData();
      setBudgetDataState({
        budgets,
        budgetCategories: [], // Categories will be loaded when budget is selected
        isLoading: false,
        isLoaded: true
      });

      // Set default budget if not already set
      if (!formData.budget && budgets.length > 0) {
        const defaultBudgetId = budgets[0].id || budgets[0]._id;
        if (defaultBudgetId) {
          setFormData(prev => ({ ...prev, budget: defaultBudgetId }));
          // Load categories for the default budget
          handleBudgetChange(defaultBudgetId);
        }
      }
    } catch (error) {
      console.error('Error loading budget data:', error);
      setBudgetDataState(prev => ({ ...prev, isLoading: false }));
    }
  }, [budgetDataState.isLoaded, budgetDataState.isLoading, formData.budget, handleBudgetChange]);

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.description.trim()) {
      newErrors.description = 'Description is required';
    }
    if (!formData.amount.trim()) {
      newErrors.amount = 'Amount is required';
    } else if (isNaN(Number(formData.amount)) || Number(formData.amount) <= 0) {
      newErrors.amount = 'Amount must be a positive number';
    }
    if (!formData.reference.trim()) {
      newErrors.reference = 'Reference is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    try {
      // Helper function to sanitize ObjectId fields (convert empty strings to undefined)
      const sanitizeObjectId = (value: string | undefined) => {
        if (!value || value.trim() === '') {
          return undefined;
        }
        return value.trim();
      };

      // Create properly mapped submission data for the Expense model
      const submissionData = {
        date: formData.date,
        category: formData.description, // Map description to category (required field)
        subcategory: formData.subcategory || undefined, // Convert empty string to undefined
        amount: Number(formData.amount), // Convert to number for the model
        reference: formData.reference,
        description: formData.notes, // Map notes to description (optional details)
        fiscalYear: formData.fiscalYear,
        status: formData.status,
        paymentMethod: formData.paymentMethod || undefined,
        vendor: formData.vendor || undefined,
        department: formData.department || undefined,
        costCenter: formData.costCenter || undefined,
        // Sanitize budget-related ObjectId fields to prevent validation errors
        budget: sanitizeObjectId(formData.budget),
        budgetCategory: sanitizeObjectId(formData.budgetCategory),
        budgetSubcategory: sanitizeObjectId(formData.budgetSubcategory),
        appliedToBudget: formData.appliedToBudget,
        notes: formData.notes || undefined, // Additional notes
      };

      console.log('Submitting expenditure data:', submissionData);

      await onSubmit(submissionData);
      toast({
        title: expenditure ? 'Expenditure updated' : 'Expenditure created',
        description: expenditure
          ? 'The expenditure has been updated successfully.'
          : 'The expenditure has been created successfully.',
      });
    } catch (error) {
      console.error('Error submitting expenditure:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'An error occurred while saving the expenditure',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Receipt className="h-5 w-5" />
            {expenditure ? 'Edit Expenditure' : 'Record New Expenditure'}
          </CardTitle>
          <Badge variant="default" className="text-xs">
            <Zap className="h-3 w-3 mr-1" />
            Progressive Loading
          </Badge>
        </div>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Instant Loading Section - No Backend Dependencies */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-medium">Basic Information</h3>
              <Badge variant="outline" className="text-xs">
                <CheckCircle className="h-3 w-3 mr-1" />
                Instant Load
              </Badge>
            </div>
            <Separator />

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Date */}
              <div className="space-y-2">
                <Label htmlFor="date">Date *</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className="w-full justify-start text-left font-normal"
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {formData.date ? format(formData.date, 'PPP') : 'Pick a date'}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={formData.date}
                      onSelect={(date) => date && updateField('date', date)}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>

              {/* Expense Title/Category */}
              <div className="space-y-2">
                <Label htmlFor="description">Expense Title/Category *</Label>
                <Input
                  id="description"
                  value={formData.description}
                  onChange={(e) => updateField('description', e.target.value)}
                  placeholder="Enter expense title (e.g., Office supplies, Travel expenses, Equipment purchase)"
                  disabled={isSubmitting}
                />
                {errors.description && <p className="text-sm text-destructive">{errors.description}</p>}
              </div>

              {/* Amount */}
              <div className="space-y-2">
                <Label htmlFor="amount">Amount (MWK) *</Label>
                <Input
                  id="amount"
                  type="number"
                  step="0.01"
                  placeholder="Enter amount"
                  value={formData.amount}
                  onChange={(e) => updateField('amount', e.target.value)}
                />
                {errors.amount && <p className="text-sm text-destructive">{errors.amount}</p>}
              </div>

              {/* Reference */}
              <div className="space-y-2">
                <Label htmlFor="reference">Reference *</Label>
                <Input
                  id="reference"
                  placeholder="Enter reference number"
                  value={formData.reference}
                  onChange={(e) => updateField('reference', e.target.value)}
                />
                {errors.reference && <p className="text-sm text-destructive">{errors.reference}</p>}
              </div>

              {/* Fiscal Year */}
              <div className="space-y-2">
                <Label htmlFor="fiscalYear">Fiscal Year</Label>
                <Select
                  value={formData.fiscalYear}
                  onValueChange={(value) => updateField('fiscalYear', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select fiscal year" />
                  </SelectTrigger>
                  <SelectContent>
                    {INSTANT_FORM_DATA.fiscalYears.map(year => (
                      <SelectItem key={year.value} value={year.value}>
                        {year.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Status */}
              <div className="space-y-2">
                <Label htmlFor="status">Status</Label>
                <Select
                  value={formData.status}
                  onValueChange={(value) => updateField('status', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    {INSTANT_FORM_DATA.statusOptions.map(status => (
                      <SelectItem key={status.value} value={status.value}>
                        {status.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Payment Method */}
              <div className="space-y-2">
                <Label htmlFor="paymentMethod">Payment Method</Label>
                <Select
                  value={formData.paymentMethod || ''}
                  onValueChange={(value) => updateField('paymentMethod', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select payment method" />
                  </SelectTrigger>
                  <SelectContent>
                    {INSTANT_FORM_DATA.paymentMethods.map(method => (
                      <SelectItem key={method.value} value={method.value}>
                        {method.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Department */}
              <div className="space-y-2">
                <Label htmlFor="department">Department</Label>
                <Select
                  value={formData.department || ''}
                  onValueChange={(value) => updateField('department', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select department" />
                  </SelectTrigger>
                  <SelectContent>
                    {INSTANT_FORM_DATA.departments.map(dept => (
                      <SelectItem key={dept.value} value={dept.value}>
                        {dept.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Vendor */}
              <div className="space-y-2">
                <Label htmlFor="vendor">Vendor/Supplier</Label>
                <Input
                  id="vendor"
                  placeholder="Enter vendor name"
                  value={formData.vendor || ''}
                  onChange={(e) => updateField('vendor', e.target.value)}
                />
              </div>

              {/* Cost Center */}
              <div className="space-y-2">
                <Label htmlFor="costCenter">Cost Center</Label>
                <Input
                  id="costCenter"
                  placeholder="Enter cost center"
                  value={formData.costCenter || ''}
                  onChange={(e) => updateField('costCenter', e.target.value)}
                />
              </div>
            </div>
          </div>

          {/* Progressive Loading Section - Backend Dependencies */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-medium">Budget Integration (Optional)</h3>
              <Badge variant="outline" className="text-xs">
                {budgetDataState.isLoaded ? (
                  <>
                    <CheckCircle className="h-3 w-3 mr-1" />
                    Loaded
                  </>
                ) : (
                  <>
                    <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                    On-Demand
                  </>
                )}
              </Badge>
            </div>
            <Separator />

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Budget Selection */}
              <div className="space-y-2">
                <Label htmlFor="budget">Budget</Label>
                {budgetDataState.isLoading ? (
                  <Skeleton className="h-10 w-full" />
                ) : (
                  <Select
                    value={formData.budget || ''}
                    onValueChange={handleBudgetChange}
                    onOpenChange={(open) => {
                      if (open) handleBudgetDropdownOpen();
                    }}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select budget (optional)" />
                    </SelectTrigger>
                    <SelectContent>
                      {budgetDataState.budgets.map(budget => {
                        const budgetId = budget.id || budget._id || '';
                        if (!budgetId) return null;

                        return (
                          <SelectItem key={budgetId} value={budgetId}>
                            {budget.name} ({budget.fiscalYear})
                          </SelectItem>
                        );
                      })}
                    </SelectContent>
                  </Select>
                )}
              </div>

              {/* Budget Category Selection */}
              <div className="space-y-2">
                <Label htmlFor="budgetCategory">Budget Category</Label>
                {budgetDataState.isLoading ? (
                  <Skeleton className="h-10 w-full" />
                ) : (
                  <Select
                    value={formData.budgetCategory || ''}
                    onValueChange={(value) => updateField('budgetCategory', value)}
                    disabled={!formData.budget || budgetDataState.budgetCategories.length === 0}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder={
                        !formData.budget
                          ? "Select budget first"
                          : budgetDataState.budgetCategories.length === 0
                            ? "No categories available"
                            : "Select category (optional)"
                      } />
                    </SelectTrigger>
                    <SelectContent>
                      {budgetDataState.budgetCategories.map(category => {
                        const categoryId = category.id || category._id || '';
                        if (!categoryId) return null;

                        return (
                          <SelectItem key={categoryId} value={categoryId}>
                            <div className="flex flex-col items-start">
                              <span className="font-medium">{category.name}</span>
                              {category.description && (
                                <span className="text-xs text-muted-foreground">{category.description}</span>
                              )}
                              <div className="flex items-center gap-2 text-xs text-muted-foreground">
                                <span>Budgeted: MWK {category.budgetedAmount?.toLocaleString() || 0}</span>
                                <span>•</span>
                                <span>Used: MWK {category.actualAmount?.toLocaleString() || 0}</span>
                              </div>
                            </div>
                          </SelectItem>
                        );
                      })}
                    </SelectContent>
                  </Select>
                )}
                {formData.budget && budgetDataState.budgetCategories.length === 0 && !budgetDataState.isLoading && (
                  <p className="text-sm text-muted-foreground">
                    No expense categories found for this budget. You can still create the expenditure without a category.
                  </p>
                )}
              </div>
            </div>
          </div>



          {/* Additional Details */}
          <div className="space-y-2">
            <Label htmlFor="notes">Additional Details</Label>
            <Textarea
              id="notes"
              placeholder="Enter additional details about this expenditure (optional)"
              value={formData.notes || ''}
              onChange={(e) => updateField('notes', e.target.value)}
              rows={3}
            />
          </div>
        </form>
      </CardContent>
      <CardFooter className="flex justify-end space-x-2">
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
          disabled={isSubmitting}
        >
          Cancel
        </Button>
        <Button
          type="submit"
          onClick={handleSubmit}
          disabled={isSubmitting || isLoading}
        >
          {isSubmitting ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              {expenditure ? 'Updating...' : 'Creating...'}
            </>
          ) : (
            expenditure ? 'Update Expenditure' : 'Create Expenditure'
          )}
        </Button>
      </CardFooter>
    </Card>
  );
}
