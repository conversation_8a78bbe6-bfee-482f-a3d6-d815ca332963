// app/api/accounting/expenditure/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/database';
import { logger } from '@/lib/utils/logger';
import { z } from 'zod';
import Expense from '@/models/accounting/Expense';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { formatISODate } from '@/lib/utils/date-formatter';
import { IUser } from '@/models/User';

export const runtime = 'nodejs';

// Expense model validation schema
const expenditureSchema = z.object({
  // Required fields for Expense model
  description: z.string().min(1, "Description is required"),
  amount: z.number().positive("Amount must be positive"),
  expenditureDate: z.union([
    z.string().refine((val) => !isNaN(Date.parse(val)), {
      message: "Invalid expenditure date format",
    }),
    z.date()
  ]).transform((val) => typeof val === 'string' ? val : val.toISOString()),

  // Optional fields
  department: z.string().default('general'),
  costCenter: z.string().optional().nullable(),
  fiscalYear: z.string().default('2025-2026'),
  appliedToBudget: z.boolean().default(false),
  budget: z.string().optional().nullable(),
  budgetCategory: z.string().optional().nullable(),
  budgetSubcategory: z.string().optional().nullable(),
  notes: z.string().optional().nullable(),

  // Vendor info (simplified)
  vendor: z.object({
    vendorName: z.string().optional()
  }).optional(),
});

export async function GET(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const id = searchParams.get('id');
    const status = searchParams.get('status');
    const fiscalYear = searchParams.get('fiscalYear');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');

    // If ID is provided, get a specific expenditure
    if (id) {
      const expenditure = await Expense.findById(id)
        .populate('createdBy', 'firstName lastName email')
        .populate('updatedBy', 'firstName lastName email');

      if (!expenditure) {
        return NextResponse.json(
          { error: 'Expenditure not found' },
          { status: 404 }
        );
      }

      return NextResponse.json({ expenditure });
    }

    // Build filter for listing expenditures
    const filter: Record<string, any> = {};

    if (status) {
      filter.status = status.toUpperCase();
    }

    if (fiscalYear) {
      filter.fiscalYear = fiscalYear;
    }

    // Get expenditures with pagination
    const totalCount = await Expense.countDocuments(filter);
    const totalPages = Math.ceil(totalCount / limit);
    const skip = (page - 1) * limit;

    const expenditures = await Expense.find(filter)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .populate('createdBy', 'name email')
      .populate('updatedBy', 'name email');

    return NextResponse.json({
      expenditures,
      pagination: {
        totalCount,
        totalPages,
        currentPage: page
      }
    });
  } catch (error: unknown) {
    logger.error('Error fetching expenditure data', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal Server Error' },
      { status: 500 }
    );
  }
}

export async function POST(req: NextRequest) {
  try {

    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Get request body
    const rawData = await req.json();

    // Create validation data (for Zod schema)
    const validationData = {
      // Fields expected by validation schema
      description: rawData.description || 'Expenditure Entry',
      amount: Number(rawData.amount),
      expenditureDate: rawData.expenditureDate, // Keep original field name for validation
      department: rawData.department || 'general',
      costCenter: rawData.costCenter || undefined,
      fiscalYear: rawData.fiscalYear || '2025-2026',
      appliedToBudget: rawData.appliedToBudget || false,
      budget: rawData.budget || undefined,
      budgetCategory: rawData.budgetCategory || undefined,
      budgetSubcategory: rawData.budgetSubcategory || undefined,
      notes: rawData.notes || undefined,
      vendor: rawData.vendor || undefined, // Keep as object for validation
    };

    // Create expenditure data for Expense model (after validation)
    const expenditureData = {
      // Required fields for Expense model
      date: new Date(rawData.expenditureDate),
      category: rawData.description || 'General Expenditure',
      subcategory: rawData.costCenter || undefined,
      amount: Number(rawData.amount),
      reference: `EXP-${Date.now()}-${Math.random().toString(36).substring(2, 8)}`,
      description: rawData.description || 'Expenditure Entry',
      fiscalYear: rawData.fiscalYear || '2025-2026',
      status: 'draft',

      // Optional fields
      paymentMethod: rawData.paymentMethod || undefined,
      budget: rawData.budget || undefined,
      budgetCategory: rawData.budgetCategory || undefined,
      budgetSubcategory: rawData.budgetSubcategory || undefined,
      appliedToBudget: rawData.appliedToBudget || false,
      notes: rawData.notes || undefined,
      vendor: rawData.vendor?.vendorName || undefined, // Extract vendorName for Expense model
      department: rawData.department || 'general',
      costCenter: rawData.costCenter || undefined,

      // Audit fields
      createdBy: user.id,
      updatedBy: user.id
    };

    console.log('Sanitized expenditure data:', JSON.stringify(sanitizedData, null, 2));

    // Validate request body
    const validationResult = expenditureSchema.safeParse(sanitizedData);
    if (!validationResult.success) {
      console.error('Validation error:', validationResult.error.errors);
      return NextResponse.json(
        { 
          error: validationResult.error.errors[0].message,
          details: validationResult.error.errors
        },
        { status: 400 }
      );
    }

    // Create final expenditure data with all required fields
    const expenditureData = {
      ...sanitizedData, // Use sanitized data instead of validation result
      createdBy: user.id,
      updatedBy: user.id
    };

    // Create expenditure using Expense model
    const expenditure = await Expense.create(expenditureData);

    return NextResponse.json(
      {
        success: true,
        message: 'Expenditure created successfully',
        expenditure
      },
      { status: 201 }
    );

  } catch (error: unknown) {
    logger.error('Error creating expenditure', error);

    // Handle specific error types
    if (error instanceof Error) {
      if (error.message.includes('aborted')) {
        return NextResponse.json(
          { error: 'Request was cancelled - please try again' },
          { status: 499 }
        );
      }
    }

    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal Server Error' },
      { status: 500 }
    );
  }
}
