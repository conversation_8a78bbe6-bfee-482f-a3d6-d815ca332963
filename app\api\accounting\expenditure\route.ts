// app/api/accounting/expenditure/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/database';
import { logger } from '@/lib/utils/logger';
import { z } from 'zod';
import { Expenditure, ExpenditureCategory, ExpenditureSubcategory, ExpenditureStatus, ExpenditurePriority, PaymentMethod } from '@/models/accounting/Expenditure';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';

export const runtime = 'nodejs';

// Enhanced expenditure schema for validation
const expenditureSchema = z.object({
  title: z.string().min(1, "Title is required"),
  description: z.string().min(1, "Description is required"),
  category: z.string().min(1, "Category is required"),
  subcategory: z.string().min(1, "Subcategory is required"),
  amount: z.number().positive("Amount must be positive"),
  currency: z.string().default('MWK'),
  exchangeRate: z.number().default(1),
  amountInBaseCurrency: z.number().positive("Amount in base currency must be positive"),
  expenditureDate: z.string().refine((val) => !isNaN(Date.parse(val)), {
    message: "Invalid expenditure date format",
  }),
  status: z.string().default('DRAFT'),
  priority: z.string().default('MEDIUM'),
  requestedBy: z.string().optional(),
  requestedByName: z.string().optional(),
  requestedByEmail: z.string().optional(),
  department: z.string().optional(),
  costCenter: z.string().optional().nullable(),
  vendor: z.object({
    vendorName: z.string(),
    vendorId: z.string().optional().nullable(),
    vendorEmail: z.string().optional().nullable(),
    vendorPhone: z.string().optional().nullable(),
    vendorAddress: z.string().optional().nullable(),
  }),
  budgetAllocations: z.array(z.object({
    budgetId: z.string().optional().nullable(),
    categoryId: z.string().optional().nullable(),
    subcategoryId: z.string().optional().nullable(),
    allocatedAmount: z.number(),
    percentage: z.number(),
  })).default([]),
  paymentMethod: z.string().default('BANK_TRANSFER'),
  fiscalYear: z.string().optional(),
  appliedToBudget: z.boolean().default(false),
  tags: z.array(z.string()).default([]),
  notes: z.array(z.object({
    note: z.string(),
    createdAt: z.date().optional(),
  })).default([]),
  isUrgent: z.boolean().default(false),
  requiresReceipt: z.boolean().default(true),
  isCapitalExpenditure: z.boolean().default(false),
});

export async function GET(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const id = searchParams.get('id');
    const status = searchParams.get('status');
    const fiscalYear = searchParams.get('fiscalYear');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');

    // If ID is provided, get a specific expenditure
    if (id) {
      const expenditure = await Expenditure.findById(id)
        .populate('createdBy', 'name email')
        .populate('updatedBy', 'name email');

      if (!expenditure) {
        return NextResponse.json(
          { error: 'Expenditure not found' },
          { status: 404 }
        );
      }

      return NextResponse.json({ expenditure });
    }

    // Build filter for listing expenditures
    const filter: Record<string, any> = {};

    if (status) {
      filter.status = status.toUpperCase();
    }

    if (fiscalYear) {
      filter.fiscalYear = fiscalYear;
    }

    // Get expenditures with pagination
    const totalCount = await Expenditure.countDocuments(filter);
    const totalPages = Math.ceil(totalCount / limit);
    const skip = (page - 1) * limit;

    const expenditures = await Expenditure.find(filter)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .populate('createdBy', 'name email')
      .populate('updatedBy', 'name email');

    return NextResponse.json({
      expenditures,
      pagination: {
        totalCount,
        totalPages,
        currentPage: page
      }
    });
  } catch (error: unknown) {
    logger.error('Error fetching expenditure data', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal Server Error' },
      { status: 500 }
    );
  }
}

export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Get request body
    const rawData = await req.json();

    // Sanitize data
    const sanitizedData = {
      ...rawData,
      // Convert date strings to Date objects
      expenditureDate: new Date(rawData.expenditureDate),
      // Set user information
      requestedBy: user.id,
      requestedByName: user.name || 'Unknown User',
      requestedByEmail: user.email || '<EMAIL>',
      // Generate expenditure number
      expenditureNumber: `EXP-${Date.now()}`,
      // Set timestamps for notes
      notes: rawData.notes?.map((note: any) => ({
        ...note,
        createdAt: note.createdAt || new Date()
      })) || [],
      // Add required fields with defaults
      taxInfo: {
        taxType: 'none',
        taxRate: 0,
        taxAmount: 0,
        isExempt: true
      },
      approvalWorkflow: [],
      currentApprovalStep: 0,
      receipts: [],
      supportingDocuments: [],
      policyCompliance: {
        isCompliant: true,
        violations: [],
        requiresApproval: false,
        approvalLevel: 0
      }
    };

    console.log('Sanitized expenditure data:', sanitizedData);

    // Validate request body
    const validationResult = expenditureSchema.safeParse(sanitizedData);
    if (!validationResult.success) {
      console.error('Validation error:', validationResult.error.errors);
      return NextResponse.json(
        { 
          error: validationResult.error.errors[0].message,
          details: validationResult.error.errors
        },
        { status: 400 }
      );
    }

    // Add created by
    const expenditureData = {
      ...validationResult.data,
      createdBy: user.id
    };

    // Create expenditure
    const expenditure = await Expenditure.create(expenditureData);

    return NextResponse.json(
      {
        success: true,
        message: 'Expenditure created successfully',
        expenditure
      },
      { status: 201 }
    );
  } catch (error: unknown) {
    logger.error('Error creating expenditure', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal Server Error' },
      { status: 500 }
    );
  }
}
