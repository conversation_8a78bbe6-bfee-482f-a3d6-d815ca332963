// app/api/accounting/expenditure/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/database';
import { logger } from '@/lib/utils/logger';
import { z } from 'zod';
import Expenditure, { ExpenditureCategory, ExpenditureSubcategory, ExpenditureStatus, ExpenditurePriority, PaymentMethod } from '@/models/accounting/Expenditure';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';

export const runtime = 'nodejs';

// Simplified expenditure schema for validation
const expenditureSchema = z.object({
  // Required fields
  title: z.string().min(1, "Title is required"),
  description: z.string().min(1, "Description is required"),
  amount: z.number().positive("Amount must be positive"),
  expenditureDate: z.union([
    z.string().refine((val) => !isNaN(Date.parse(val)), {
      message: "Invalid expenditure date format",
    }),
    z.date()
  ]).transform((val) => typeof val === 'string' ? val : val.toISOString()),

  // Optional fields with defaults
  department: z.string().default('general'),
  costCenter: z.string().optional().nullable(),
  fiscalYear: z.string().optional(),
  appliedToBudget: z.boolean().default(false),
  tags: z.array(z.string()).default([]),

  // Vendor info (simplified)
  vendor: z.object({
    vendorName: z.string().default('Unknown Vendor')
  }).optional(),
});

export async function GET(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const id = searchParams.get('id');
    const status = searchParams.get('status');
    const fiscalYear = searchParams.get('fiscalYear');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');

    // If ID is provided, get a specific expenditure
    if (id) {
      const expenditure = await Expenditure.findById(id)
        .populate('createdBy', 'name email')
        .populate('updatedBy', 'name email');

      if (!expenditure) {
        return NextResponse.json(
          { error: 'Expenditure not found' },
          { status: 404 }
        );
      }

      return NextResponse.json({ expenditure });
    }

    // Build filter for listing expenditures
    const filter: Record<string, any> = {};

    if (status) {
      filter.status = status.toUpperCase();
    }

    if (fiscalYear) {
      filter.fiscalYear = fiscalYear;
    }

    // Get expenditures with pagination
    const totalCount = await Expenditure.countDocuments(filter);
    const totalPages = Math.ceil(totalCount / limit);
    const skip = (page - 1) * limit;

    const expenditures = await Expenditure.find(filter)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .populate('createdBy', 'name email')
      .populate('updatedBy', 'name email');

    return NextResponse.json({
      expenditures,
      pagination: {
        totalCount,
        totalPages,
        currentPage: page
      }
    });
  } catch (error: unknown) {
    logger.error('Error fetching expenditure data', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal Server Error' },
      { status: 500 }
    );
  }
}

export async function POST(req: NextRequest) {
  let timeoutId: NodeJS.Timeout | null = null;

  try {

    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Get request body
    const rawData = await req.json();

    // Create simplified expenditure data with only required fields
    const sanitizedData = {
      // Required basic fields
      expenditureNumber: `EXP-${Date.now()}-${Math.random().toString(36).substring(2, 8)}`,
      title: rawData.title || 'Expenditure Entry',
      description: rawData.description || rawData.title || 'Expenditure Entry',
      category: 'OTHER', // Use valid enum value
      subcategory: 'OTHER', // Use valid enum value
      amount: Number(rawData.amount),
      currency: 'MWK',
      exchangeRate: 1,
      amountInBaseCurrency: Number(rawData.amount),
      expenditureDate: new Date(rawData.expenditureDate),

      // Required user fields
      requestedBy: user.id,
      requestedByName: user.name || 'Unknown User',
      requestedByEmail: user.email || '<EMAIL>',
      department: rawData.department || 'general',

      // Required vendor field (simplified)
      vendor: {
        vendorName: rawData.vendor?.vendorName || 'Unknown Vendor'
      },

      // Optional fields
      costCenter: rawData.costCenter,
      fiscalYear: rawData.fiscalYear,
      appliedToBudget: rawData.appliedToBudget || false,

      // Status and priority
      status: 'DRAFT',
      priority: 'MEDIUM',

      // Empty arrays for optional complex fields
      budgetAllocations: [],
      approvalWorkflow: [],
      receipts: [],
      supportingDocuments: [],
      notes: [],
      tags: rawData.tags || [],

      // Timestamps
      createdBy: user.id,
      updatedBy: user.id
    };

    console.log('Sanitized expenditure data:', JSON.stringify(sanitizedData, null, 2));

    // Validate request body
    const validationResult = expenditureSchema.safeParse(sanitizedData);
    if (!validationResult.success) {
      console.error('Validation error:', validationResult.error.errors);
      return NextResponse.json(
        { 
          error: validationResult.error.errors[0].message,
          details: validationResult.error.errors
        },
        { status: 400 }
      );
    }

    // Add created by
    const expenditureData = {
      ...validationResult.data,
      createdBy: user.id
    };

    // Create expenditure
    const expenditure = await Expenditure.create(expenditureData);

    return NextResponse.json(
      {
        success: true,
        message: 'Expenditure created successfully',
        expenditure
      },
      { status: 201 }
    );

  } catch (error: unknown) {
    logger.error('Error creating expenditure', error);

    // Handle specific error types
    if (error instanceof Error) {
      if (error.message.includes('aborted')) {
        return NextResponse.json(
          { error: 'Request was cancelled - please try again' },
          { status: 499 }
        );
      }
    }

    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal Server Error' },
      { status: 500 }
    );
  }
}
