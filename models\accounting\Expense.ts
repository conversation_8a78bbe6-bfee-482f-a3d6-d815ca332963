import mongoose, { Schema, Document } from 'mongoose';

export interface IExpenseApprovalHistory {
  approver: mongoose.Types.ObjectId;
  status: 'approved' | 'rejected';
  date: Date;
  comments?: string;
  level: number;
}

export interface IExpenseApprovalWorkflow {
  currentApprover?: mongoose.Types.ObjectId;
  currentLevel: number;
  status: 'pending' | 'approved' | 'rejected';
  approvalHistory: IExpenseApprovalHistory[];
  requiredApprovers: {
    level: number;
    approver: mongoose.Types.ObjectId;
    role: string;
    amountThreshold?: number;
  }[];
  autoApprovalRules?: {
    maxAmount?: number;
    categories?: string[];
    skipApproval?: boolean;
  };
}

export interface IExpenseStatusHistory {
  status: 'draft' | 'pending_approval' | 'approved' | 'rejected' | 'paid' | 'cancelled';
  changedBy: mongoose.Types.ObjectId;
  changedAt: Date;
  notes?: string;
  reason?: string;
}

export interface IExpense extends Document {
  date: Date;
  category: string;
  subcategory?: string;
  amount: number;
  reference: string;
  description?: string;
  fiscalYear: string;
  status: 'draft' | 'pending_approval' | 'approved' | 'rejected' | 'paid' | 'cancelled';
  paymentMethod?: string;
  bankAccount?: mongoose.Types.ObjectId;
  budget?: mongoose.Types.ObjectId;
  budgetCategory?: mongoose.Types.ObjectId;
  budgetSubcategory?: mongoose.Types.ObjectId;
  appliedToBudget?: boolean;
  attachments?: string[];
  notes?: string;

  // Enhanced approval workflow (matching Income model)
  approvalWorkflow?: IExpenseApprovalWorkflow;

  // Status timestamps (matching Income model)
  submittedAt?: Date;
  approvedAt?: Date;
  rejectedAt?: Date;
  paidAt?: Date;
  cancelledAt?: Date;

  // Status change tracking (matching Income model)
  approvedBy?: mongoose.Types.ObjectId;
  rejectedBy?: mongoose.Types.ObjectId;
  paidBy?: mongoose.Types.ObjectId;
  cancelledBy?: mongoose.Types.ObjectId;
  rejectionReason?: string;
  statusHistory?: IExpenseStatusHistory[];

  // Budget impact tracking (NEW - for expenditure integration)
  budgetImpact?: {
    budgetId: mongoose.Types.ObjectId;
    categoryId: mongoose.Types.ObjectId;
    subcategoryId?: mongoose.Types.ObjectId;
    impactAmount: number; // Negative for expenses
    utilizationPercentage: number;
    varianceCreated: number;
  };

  // Vendor and department info (enhanced)
  vendor?: string;
  department?: string;
  costCenter?: string;

  // Audit fields
  createdBy: mongoose.Types.ObjectId;
  updatedBy?: mongoose.Types.ObjectId;
  createdAt: Date;
  updatedAt: Date;
}

const ExpenseSchema: Schema = new Schema(
  {
    date: {
      type: Date,
      required: true,
    },
    category: {
      type: String,
      required: true,
      trim: true,
    },
    subcategory: {
      type: String,
      trim: true,
    },
    amount: {
      type: Number,
      required: true,
    },
    reference: {
      type: String,
      required: true,
      trim: true,
    },
    description: {
      type: String,
      trim: true,
    },
    fiscalYear: {
      type: String,
      required: true,
      trim: true,
    },
    status: {
      type: String,
      required: true,
      enum: ['draft', 'pending_approval', 'approved', 'rejected', 'paid', 'cancelled'],
      default: 'draft',
    },
    paymentMethod: {
      type: String,
      trim: true,
    },
    bankAccount: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'BankAccount',
    },
    budget: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Budget',
    },
    budgetCategory: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'BudgetCategory',
    },
    budgetSubcategory: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'BudgetSubcategory',
    },
    appliedToBudget: {
      type: Boolean,
      default: false,
    },
    attachments: [{
      type: String,
    }],
    notes: {
      type: String,
      trim: true,
    },
    approvalWorkflow: {
      currentApprover: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
      },
      currentLevel: {
        type: Number,
        default: 0,
      },
      status: {
        type: String,
        enum: ['pending', 'approved', 'rejected'],
        default: 'pending',
      },
      approvalHistory: [{
        approver: {
          type: mongoose.Schema.Types.ObjectId,
          ref: 'User',
          required: true,
        },
        status: {
          type: String,
          enum: ['approved', 'rejected'],
          required: true,
        },
        date: {
          type: Date,
          required: true,
        },
        comments: {
          type: String,
          trim: true,
        },
        level: {
          type: Number,
          required: true,
        },
      }],
      requiredApprovers: [{
        level: {
          type: Number,
          required: true,
        },
        approver: {
          type: mongoose.Schema.Types.ObjectId,
          ref: 'User',
          required: true,
        },
        role: {
          type: String,
          required: true,
        },
        amountThreshold: {
          type: Number,
        },
      }],
      autoApprovalRules: {
        maxAmount: {
          type: Number,
        },
        categories: [{
          type: String,
        }],
        skipApproval: {
          type: Boolean,
          default: false,
        },
      },
    },

    // Status timestamps (matching Income model)
    submittedAt: {
      type: Date,
    },
    approvedAt: {
      type: Date,
    },
    rejectedAt: {
      type: Date,
    },
    paidAt: {
      type: Date,
    },
    cancelledAt: {
      type: Date,
    },

    // Status change tracking (matching Income model)
    approvedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
    },
    rejectedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
    },
    paidBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
    },
    cancelledBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
    },
    rejectionReason: {
      type: String,
      trim: true,
    },
    statusHistory: [{
      status: {
        type: String,
        required: true,
        enum: ['draft', 'pending_approval', 'approved', 'rejected', 'paid', 'cancelled'],
      },
      changedBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        required: true,
      },
      changedAt: {
        type: Date,
        required: true,
        default: Date.now,
      },
      notes: {
        type: String,
        trim: true,
      },
      reason: {
        type: String,
        trim: true,
      },
    }],

    // Budget impact tracking (NEW)
    budgetImpact: {
      budgetId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Budget',
      },
      categoryId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'BudgetCategory',
      },
      subcategoryId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'BudgetSubcategory',
      },
      impactAmount: {
        type: Number, // Negative for expenses
      },
      utilizationPercentage: {
        type: Number,
        default: 0,
      },
      varianceCreated: {
        type: Number,
        default: 0,
      },
    },

    // Enhanced vendor and department info
    vendor: {
      type: String,
      trim: true,
    },
    department: {
      type: String,
      trim: true,
    },
    costCenter: {
      type: String,
      trim: true,
    },

    // Audit fields
    createdBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
    updatedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
    },
  },
  {
    timestamps: true,
  }
);

// Create indexes for better query performance
ExpenseSchema.index({ date: 1 });
ExpenseSchema.index({ category: 1 });
ExpenseSchema.index({ fiscalYear: 1 });
ExpenseSchema.index({ status: 1 });
ExpenseSchema.index({ reference: 1 });
ExpenseSchema.index({ budgetCategory: 1 });
ExpenseSchema.index({ budgetSubcategory: 1 });
ExpenseSchema.index({ 'approvalWorkflow.currentApprover': 1 });
ExpenseSchema.index({ 'approvalWorkflow.status': 1 });
ExpenseSchema.index({ 'approvalWorkflow.currentLevel': 1 });
ExpenseSchema.index({ submittedAt: 1 });
ExpenseSchema.index({ approvedAt: 1 });
ExpenseSchema.index({ paidAt: 1 });
ExpenseSchema.index({ vendor: 1 });
ExpenseSchema.index({ department: 1 });
ExpenseSchema.index({ costCenter: 1 });
ExpenseSchema.index({ amount: -1 });
ExpenseSchema.index({ createdAt: -1 });

// Enhanced middleware to update budget when expense is created, updated, or deleted
ExpenseSchema.post('save', async function(doc) {
  try {
    // Update budget fund for all expense statuses (draft, approved, paid)
    const { budgetFundService } = require('@/lib/services/accounting/budget-fund-service');
    await budgetFundService.handleExpenseChange((doc._id as mongoose.Types.ObjectId).toString(), (doc.createdBy as mongoose.Types.ObjectId)?.toString() || (doc.updatedBy as mongoose.Types.ObjectId)?.toString());

    // Also update traditional budget if linked and approved/paid
    if (doc.appliedToBudget && doc.budgetCategory && (doc.status === 'paid' || doc.status === 'approved')) {
      const { budgetTransactionService } = require('@/lib/services/accounting/budget-transaction-service');

      // Update budget actuals
      if (doc.budget) {
        await budgetTransactionService.updateBudgetActuals((doc.budget as mongoose.Types.ObjectId).toString(), 'expense');
      }

      // Update category actual amount
      await budgetTransactionService.updateCategoryActual((doc.budgetCategory as mongoose.Types.ObjectId).toString(), 'expense');

      console.log(`Traditional budget updated for expense ${doc._id}`);
    }

    // Create/update BudgetExpenditure record
    if (doc.appliedToBudget && doc.budget && doc.budgetCategory) {
      const { budgetExpenditureIntegrationService } = require('@/lib/services/accounting/budget-expenditure-integration');
      await budgetExpenditureIntegrationService.createExpenditureAsBudgetItem(doc);
    }

    console.log(`Budget fund updated for expense ${doc._id} with status ${doc.status}`);
  } catch (error) {
    console.error('Error updating budget after expense save:', error);
  }
});

// For findOneAndUpdate, we need to use a different approach since the post middleware
// receives the result, not the document. We'll use pre middleware to get the document ID
// and then use post middleware to trigger the update
ExpenseSchema.pre('findOneAndUpdate', async function() {
  // Store the document ID for use in post middleware
  const docToUpdate = await this.model.findOne(this.getQuery());
  if (docToUpdate) {
    (this as any)._docId = docToUpdate._id.toString();
  }
});

ExpenseSchema.post('findOneAndUpdate', async function(doc) {
  if (!doc) return;

  try {
    // Get the document ID from pre middleware or from the result
    const docId = (this as any)._docId || (doc as any)._id?.toString();
    if (!docId) return;

    // Fetch the updated document to get all properties
    const updatedDoc = await this.model.findById(docId);
    if (!updatedDoc) return;

    // Update budget fund for all expense statuses (draft, approved, paid)
    const { budgetFundService } = require('@/lib/services/accounting/budget-fund-service');
    await budgetFundService.handleExpenseChange(updatedDoc._id.toString(), updatedDoc.createdBy?.toString() || updatedDoc.updatedBy?.toString());

    // Also update traditional budget if linked and approved/paid
    if (updatedDoc.appliedToBudget && updatedDoc.budgetCategory && (updatedDoc.status === 'paid' || updatedDoc.status === 'approved')) {
      const { budgetTransactionService } = require('@/lib/services/accounting/budget-transaction-service');

      // Update budget actuals
      if (updatedDoc.budget) {
        await budgetTransactionService.updateBudgetActuals(updatedDoc.budget.toString(), 'expense');
      }

      // Update category actual amount
      await budgetTransactionService.updateCategoryActual(updatedDoc.budgetCategory.toString(), 'expense');

      console.log(`Traditional budget updated for expense ${updatedDoc._id} after update`);
    }

    // Update BudgetExpenditure record
    if (updatedDoc.appliedToBudget && updatedDoc.budget && updatedDoc.budgetCategory) {
      const { budgetExpenditureIntegrationService } = require('@/lib/services/accounting/budget-expenditure-integration');
      await budgetExpenditureIntegrationService.updateExpenditureAsBudgetItem(updatedDoc);
    }

    console.log(`Budget fund updated for expense ${updatedDoc._id} after update`);
  } catch (error) {
    console.error('Error updating budget after expense update:', error);
  }
});

// Handle document removal
ExpenseSchema.pre<mongoose.Query<any, IExpense>>('findOneAndDelete', async function() {
  try {
    // Get the document that will be deleted
    const doc = await this.model.findOne(this.getFilter()) as IExpense;

    // Only update budget if the expense had a budget category and was approved/paid
    if (doc && doc.appliedToBudget && doc.budgetCategory && (doc.status === 'paid' || doc.status === 'approved')) {
      // Import the budget transaction service
      const { budgetTransactionService } = require('@/lib/services/accounting/budget-transaction-service');

      // Update budget actuals
      if (doc.budget) {
        await budgetTransactionService.updateBudgetActuals(doc.budget.toString(), 'expense');
      }

      // Update category actual amount
      await budgetTransactionService.updateCategoryActual(doc.budgetCategory.toString(), 'expense');

      // Remove BudgetExpenditure record
      if (doc.budget && doc.budgetCategory) {
        const { budgetExpenditureIntegrationService } = require('@/lib/services/accounting/budget-expenditure-integration');
        await budgetExpenditureIntegrationService.removeExpenditureFromBudget(doc._id.toString());
      }

      console.log(`Budget updated for expense ${doc._id} before removal`);
    }
  } catch (error) {
    console.error('Error updating budget before expense removal:', error);
  }
});

export default mongoose.models.Expense || mongoose.model<IExpense>('Expense', ExpenseSchema);
