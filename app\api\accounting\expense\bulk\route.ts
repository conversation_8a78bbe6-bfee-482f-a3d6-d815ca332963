// app/api/accounting/expense/bulk/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/database';
import { logger } from '@/lib/utils/logger';
import { z } from 'zod';
import Expense from '@/models/accounting/Expense';
import { budgetExpenditureIntegrationService } from '@/lib/services/accounting/budget-expenditure-integration';
import { budgetTransactionService } from '@/lib/services/accounting/budget-transaction-service';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';

export const runtime = 'nodejs';

// Bulk expense creation schema
const bulkExpenseSchema = z.object({
  expenses: z.array(z.object({
    date: z.string().refine((val) => !isNaN(Date.parse(val)), {
      message: "Invalid date format",
    }),
    category: z.string().min(1, "Category is required"),
    subcategory: z.string().optional(),
    amount: z.number().positive("Amount must be positive"),
    reference: z.string().min(2, "Reference must be at least 2 characters"),
    description: z.string().optional(),
    fiscalYear: z.string().min(4, "Fiscal year is required"),
    status: z.enum(['draft', 'pending_approval', 'approved', 'rejected', 'paid', 'cancelled']).default('draft'),
    paymentMethod: z.string().optional(),
    budget: z.string().optional(),
    budgetCategory: z.string().optional(),
    budgetSubcategory: z.string().optional(),
    appliedToBudget: z.boolean().optional().default(true),
    vendor: z.string().optional(),
    department: z.string().optional(),
    costCenter: z.string().optional(),
    notes: z.string().optional(),
  })).min(1, "At least one expense is required")
});

// Bulk status update schema
const bulkStatusUpdateSchema = z.object({
  expenseIds: z.array(z.string()).min(1, "At least one expense ID is required"),
  status: z.enum(['draft', 'pending_approval', 'approved', 'rejected', 'paid', 'cancelled']),
  notes: z.string().optional(),
  reason: z.string().optional(),
});

// Bulk delete schema
const bulkDeleteSchema = z.object({
  expenseIds: z.array(z.string()).min(1, "At least one expense ID is required"),
  reason: z.string().min(1, "Deletion reason is required"),
});

/**
 * POST /api/accounting/expense/bulk
 * Create multiple expenses at once
 */
export async function POST(req: NextRequest): Promise<Response> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Get request body
    const data = await req.json();

    // Validate request body
    const validationResult = bulkExpenseSchema.safeParse(data);
    if (!validationResult.success) {
      return NextResponse.json(
        { error: validationResult.error.errors[0].message },
        { status: 400 }
      );
    }

    const { expenses } = validationResult.data;

    // Process expenses in batches for better performance
    const batchSize = 10;
    const results = [];
    const errors = [];

    for (let i = 0; i < expenses.length; i += batchSize) {
      const batch = expenses.slice(i, i + batchSize);
      
      for (const expenseData of batch) {
        try {
          // Prepare expense data
          const expenseToCreate = {
            ...expenseData,
            date: new Date(expenseData.date),
            createdBy: user.id
          };

          // Create expense
          const expense = await Expense.create(expenseToCreate);

          // Handle budget integration
          if (expense.appliedToBudget && expense.budget && expense.budgetCategory) {
            try {
              await budgetExpenditureIntegrationService.createExpenditureAsBudgetItem(expense);
              
              // Update budget actuals for paid expenses
              if (expense.status === 'paid') {
                await budgetTransactionService.linkExpenseToBudget(
                  expense._id.toString(),
                  expense.budget.toString(),
                  expense.budgetCategory.toString(),
                  expense.budgetSubcategory ? expense.budgetSubcategory.toString() : undefined
                );
              }
            } catch (budgetError) {
              logger.error('Error in budget integration for bulk expense', budgetError);
              // Continue with expense creation even if budget integration fails
            }
          }

          results.push({
            success: true,
            expense: {
              id: expense._id,
              reference: expense.reference,
              amount: expense.amount,
              status: expense.status
            }
          });

        } catch (error) {
          errors.push({
            success: false,
            reference: expenseData.reference,
            error: error instanceof Error ? error.message : 'Unknown error'
          });
        }
      }
    }

    // Log bulk creation
    logger.info('Bulk expense creation completed', {
      userId: user.id,
      totalRequested: expenses.length,
      successful: results.length,
      failed: errors.length
    });

    return NextResponse.json({
      success: true,
      message: `Bulk expense creation completed. ${results.length} successful, ${errors.length} failed.`,
      results,
      errors,
      summary: {
        totalRequested: expenses.length,
        successful: results.length,
        failed: errors.length
      }
    });

  } catch (error: unknown) {
    logger.error('Error in bulk expense creation', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal Server Error' },
      { status: 500 }
    );
  }
}

/**
 * PATCH /api/accounting/expense/bulk
 * Update status of multiple expenses at once
 */
export async function PATCH(req: NextRequest): Promise<Response> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT,
      UserRole.FINANCE_DIRECTOR
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Get request body
    const data = await req.json();

    // Validate request body
    const validationResult = bulkStatusUpdateSchema.safeParse(data);
    if (!validationResult.success) {
      return NextResponse.json(
        { error: validationResult.error.errors[0].message },
        { status: 400 }
      );
    }

    const { expenseIds, status, notes, reason } = validationResult.data;

    // Process status updates
    const results = [];
    const errors = [];

    for (const expenseId of expenseIds) {
      try {
        // Get existing expense
        const existingExpense = await Expense.findById(expenseId);
        if (!existingExpense) {
          errors.push({
            success: false,
            expenseId,
            error: 'Expense not found'
          });
          continue;
        }

        const oldStatus = existingExpense.status;

        // Validate status transition (basic validation)
        const validTransitions: Record<string, string[]> = {
          'draft': ['pending_approval', 'cancelled'],
          'pending_approval': ['approved', 'rejected', 'cancelled'],
          'approved': ['paid', 'cancelled'],
          'rejected': ['draft', 'cancelled'],
          'paid': ['cancelled'],
          'cancelled': []
        };

        if (!validTransitions[oldStatus]?.includes(status)) {
          errors.push({
            success: false,
            expenseId,
            error: `Invalid status transition from ${oldStatus} to ${status}`
          });
          continue;
        }

        // Prepare update data
        const updateData: any = {
          status,
          updatedBy: user.id
        };

        // Add status-specific fields
        switch (status) {
          case 'pending_approval':
            updateData.submittedAt = new Date();
            break;
          case 'approved':
            updateData.approvedAt = new Date();
            updateData.approvedBy = user.id;
            break;
          case 'rejected':
            updateData.rejectedAt = new Date();
            updateData.rejectedBy = user.id;
            if (reason) updateData.rejectionReason = reason;
            break;
          case 'paid':
            updateData.paidAt = new Date();
            updateData.paidBy = user.id;
            break;
          case 'cancelled':
            updateData.cancelledAt = new Date();
            updateData.cancelledBy = user.id;
            break;
        }

        // Add to status history
        updateData.$push = {
          statusHistory: {
            status,
            changedBy: user.id,
            changedAt: new Date(),
            notes,
            reason
          }
        };

        // Update expense
        const updatedExpense = await Expense.findByIdAndUpdate(expenseId, updateData, { new: true });

        // Handle budget integration for status changes
        if (updatedExpense && updatedExpense.appliedToBudget && updatedExpense.budget && updatedExpense.budgetCategory) {
          try {
            await budgetTransactionService.handleExpenseStatusChange(expenseId, oldStatus, status);
          } catch (budgetError) {
            logger.error('Error in budget integration for bulk status update', budgetError);
            // Continue even if budget integration fails
          }
        }

        results.push({
          success: true,
          expenseId,
          oldStatus,
          newStatus: status,
          reference: updatedExpense?.reference
        });

      } catch (error) {
        errors.push({
          success: false,
          expenseId,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }

    // Log bulk status update
    logger.info('Bulk expense status update completed', {
      userId: user.id,
      totalRequested: expenseIds.length,
      successful: results.length,
      failed: errors.length,
      newStatus: status
    });

    return NextResponse.json({
      success: true,
      message: `Bulk status update completed. ${results.length} successful, ${errors.length} failed.`,
      results,
      errors,
      summary: {
        totalRequested: expenseIds.length,
        successful: results.length,
        failed: errors.length,
        newStatus: status
      }
    });

  } catch (error: unknown) {
    logger.error('Error in bulk expense status update', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal Server Error' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/accounting/expense/bulk
 * Delete multiple expenses at once
 */
export async function DELETE(req: NextRequest): Promise<Response> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Check permissions (only high-level users can bulk delete)
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_DIRECTOR
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions for bulk deletion' },
        { status: 403 }
      );
    }

    // Get request body
    const data = await req.json();

    // Validate request body
    const validationResult = bulkDeleteSchema.safeParse(data);
    if (!validationResult.success) {
      return NextResponse.json(
        { error: validationResult.error.errors[0].message },
        { status: 400 }
      );
    }

    const { expenseIds, reason } = validationResult.data;

    // Process deletions
    const results = [];
    const errors = [];

    for (const expenseId of expenseIds) {
      try {
        // Get existing expense
        const existingExpense = await Expense.findById(expenseId);
        if (!existingExpense) {
          errors.push({
            success: false,
            expenseId,
            error: 'Expense not found'
          });
          continue;
        }

        // Check if expense can be deleted (business rules)
        if (existingExpense.status === 'paid') {
          errors.push({
            success: false,
            expenseId,
            error: 'Cannot delete paid expenses'
          });
          continue;
        }

        // Store expense info for cleanup
        const expenseInfo = {
          id: existingExpense._id,
          reference: existingExpense.reference,
          amount: existingExpense.amount,
          budget: existingExpense.budget,
          budgetCategory: existingExpense.budgetCategory,
          budgetSubcategory: existingExpense.budgetSubcategory,
          appliedToBudget: existingExpense.appliedToBudget
        };

        // Remove from budget integration first
        if (existingExpense.appliedToBudget && existingExpense.budget && existingExpense.budgetCategory) {
          try {
            await budgetTransactionService.removeExpenseFromBudget(
              expenseId,
              existingExpense.budget.toString(),
              existingExpense.budgetCategory.toString(),
              existingExpense.budgetSubcategory ? existingExpense.budgetSubcategory.toString() : undefined
            );
          } catch (budgetError) {
            logger.error('Error removing expense from budget during bulk delete', budgetError);
            // Continue with deletion even if budget cleanup fails
          }
        }

        // Delete the expense
        await Expense.findByIdAndDelete(expenseId);

        results.push({
          success: true,
          expenseId,
          reference: expenseInfo.reference,
          amount: expenseInfo.amount,
          deletedBy: user.id,
          deletedAt: new Date(),
          reason
        });

        // Log individual deletion
        logger.info('Expense deleted in bulk operation', {
          expenseId,
          reference: expenseInfo.reference,
          deletedBy: user.id,
          reason
        });

      } catch (error) {
        errors.push({
          success: false,
          expenseId,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }

    // Log bulk deletion
    logger.info('Bulk expense deletion completed', {
      userId: user.id,
      totalRequested: expenseIds.length,
      successful: results.length,
      failed: errors.length,
      reason
    });

    return NextResponse.json({
      success: true,
      message: `Bulk deletion completed. ${results.length} successful, ${errors.length} failed.`,
      results,
      errors,
      summary: {
        totalRequested: expenseIds.length,
        successful: results.length,
        failed: errors.length,
        deletedBy: user.name || user.email,
        deletedAt: new Date(),
        reason
      }
    });

  } catch (error: unknown) {
    logger.error('Error in bulk expense deletion', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal Server Error' },
      { status: 500 }
    );
  }
}
