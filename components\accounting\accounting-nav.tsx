"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import { cn } from "@/lib/utils";
import {
  BarChart4,
  PieChart,
  TrendingUp,
  TrendingDown,
  Receipt,
  Wallet,
  Building,
  FileText,
  BookOpen,
  Landmark,
  Link as LinkIcon,
  ChevronDown,
  ChevronRight,
  CreditCard,
  Shield
} from "lucide-react";
import { useState } from "react";

interface NavItem {
  title: string;
  href: string;
  icon: React.ReactNode;
  submenu?: NavItem[];
}

export function AccountingNav() {
  const pathname = usePathname() || '';
  const [expandedMenus, setExpandedMenus] = useState<string[]>([]);

  const toggleMenu = (href: string) => {
    setExpandedMenus(prev =>
      prev.includes(href)
        ? prev.filter(item => item !== href)
        : [...prev, href]
    );
  };

  const navItems: NavItem[] = [
    {
      title: "Dashboard",
      href: "/dashboard/accounting/dashboard",
      icon: <BarChart4 className="mr-2 h-4 w-4" />,
    },
    {
      title: "Budget & Planning",
      href: "/dashboard/accounting/budget",
      icon: <PieChart className="mr-2 h-4 w-4" />,
      submenu: [
        {
          title: "Planning",
          href: "/dashboard/accounting/budget/planning",
          icon: <ChevronRight className="mr-2 h-4 w-4" />,
        },
        {
          title: "Performance",
          href: "/dashboard/accounting/budget/performance",
          icon: <ChevronRight className="mr-2 h-4 w-4" />,
        },
        {
          title: "Reports",
          href: "/dashboard/accounting/budget/reports",
          icon: <ChevronRight className="mr-2 h-4 w-4" />,
        },
        {
          title: "Settings",
          href: "/dashboard/accounting/budget/settings",
          icon: <ChevronRight className="mr-2 h-4 w-4" />,
        },
        {
          title: "Allocation",
          href: "/dashboard/accounting/budget/allocation",
          icon: <ChevronRight className="mr-2 h-4 w-4" />,
        },
        {
          title: "Monitoring",
          href: "/dashboard/accounting/budget/monitoring",
          icon: <ChevronRight className="mr-2 h-4 w-4" />,
        },
        {
          title: "Revision",
          href: "/dashboard/accounting/budget/revision",
          icon: <ChevronRight className="mr-2 h-4 w-4" />,
        },
      ],
    },
    {
      title: "Income Management",
      href: "/dashboard/accounting/income",
      icon: <TrendingUp className="mr-2 h-4 w-4" />,
      submenu: [
        {
          title: "Overview",
          href: "/dashboard/accounting/income/overview",
          icon: <ChevronRight className="mr-2 h-4 w-4" />,
        },
        {
          title: "Government Funds",
          href: "/dashboard/accounting/income/government-funds",
          icon: <ChevronRight className="mr-2 h-4 w-4" />,
        },
        {
          title: "Fees",
          href: "/dashboard/accounting/income/fees",
          icon: <ChevronRight className="mr-2 h-4 w-4" />,
        },
        {
          title: "Donations",
          href: "/dashboard/accounting/income/donations",
          icon: <ChevronRight className="mr-2 h-4 w-4" />,
        },
      ],
    },
    {
      title: "Expenditure",
      href: "/dashboard/accounting/expenditure",
      icon: <TrendingDown className="mr-2 h-4 w-4" />,
      submenu: [
        {
          title: "Overview",
          href: "/dashboard/accounting/expenditure/overview",
          icon: <ChevronRight className="mr-2 h-4 w-4" />,
        },
        {
          title: "Create New",
          href: "/dashboard/accounting/expenditure/new",
          icon: <ChevronRight className="mr-2 h-4 w-4" />,
        },
        {
          title: "Categories",
          href: "/dashboard/accounting/expenditure/categories",
          icon: <ChevronRight className="mr-2 h-4 w-4" />,
        },
        {
          title: "Approvals",
          href: "/dashboard/accounting/expenditure/approvals",
          icon: <ChevronRight className="mr-2 h-4 w-4" />,
        },
      ],
    },
    {
      title: "Vouchers",
      href: "/dashboard/accounting/vouchers",
      icon: <Receipt className="mr-2 h-4 w-4" />,
      submenu: [
        {
          title: "Payment",
          href: "/dashboard/accounting/vouchers/payment",
          icon: <ChevronRight className="mr-2 h-4 w-4" />,
        },
        {
          title: "Receipt",
          href: "/dashboard/accounting/vouchers/receipt",
          icon: <ChevronRight className="mr-2 h-4 w-4" />,
        },
        {
          title: "Journal",
          href: "/dashboard/accounting/vouchers/journal",
          icon: <ChevronRight className="mr-2 h-4 w-4" />,
        },
      ],
    },
    {
      title: "Asset Management",
      href: "/dashboard/accounting/assets",
      icon: <Building className="mr-2 h-4 w-4" />,
      submenu: [
        {
          title: "Asset Register",
          href: "/dashboard/accounting/assets/register",
          icon: <ChevronRight className="mr-2 h-4 w-4" />,
        },
        {
          title: "Asset Categories",
          href: "/dashboard/accounting/assets/categories",
          icon: <ChevronRight className="mr-2 h-4 w-4" />,
        },
        {
          title: "Depreciation",
          href: "/dashboard/accounting/assets/depreciation",
          icon: <ChevronRight className="mr-2 h-4 w-4" />,
        },
        {
          title: "Maintenance",
          href: "/dashboard/accounting/assets/maintenance",
          icon: <ChevronRight className="mr-2 h-4 w-4" />,
        },
        {
          title: "Disposal",
          href: "/dashboard/accounting/assets/disposal",
          icon: <ChevronRight className="mr-2 h-4 w-4" />,
        },
      ],
    },
    {
      title: "Payroll & Benefits",
      href: "/dashboard/accounting/payroll",
      icon: <Wallet className="mr-2 h-4 w-4" />,
      submenu: [
        {
          title: "Processing",
          href: "/dashboard/accounting/payroll/processing",
          icon: <ChevronRight className="mr-2 h-4 w-4" />,
        },
        {
          title: "Salary Structure",
          href: "/dashboard/accounting/payroll/salary-structure",
          icon: <ChevronRight className="mr-2 h-4 w-4" />,
        },
        {
          title: "Allowances",
          href: "/dashboard/accounting/payroll/allowances",
          icon: <ChevronRight className="mr-2 h-4 w-4" />,
        },
        {
          title: "Deductions",
          href: "/dashboard/accounting/payroll/deductions",
          icon: <ChevronRight className="mr-2 h-4 w-4" />,
        },
        {
          title: "Reports",
          href: "/dashboard/accounting/payroll/reports",
          icon: <ChevronRight className="mr-2 h-4 w-4" />,
        },
      ],
    },
    {
      title: "Financial Reports",
      href: "/dashboard/accounting/reports",
      icon: <FileText className="mr-2 h-4 w-4" />,
      submenu: [
        {
          title: "Quarterly Reports",
          href: "/dashboard/accounting/reports/quarterly",
          icon: <ChevronRight className="mr-2 h-4 w-4" />,
        },
        {
          title: "Annual Reports",
          href: "/dashboard/accounting/reports/annual",
          icon: <ChevronRight className="mr-2 h-4 w-4" />,
        },
        {
          title: "Budget Reports",
          href: "/dashboard/accounting/reports/budget",
          icon: <ChevronRight className="mr-2 h-4 w-4" />,
        },
        {
          title: "Income Reports",
          href: "/dashboard/accounting/reports/income",
          icon: <ChevronRight className="mr-2 h-4 w-4" />,
        },
        {
          title: "Custom Reports",
          href: "/dashboard/accounting/reports/custom",
          icon: <ChevronRight className="mr-2 h-4 w-4" />,
        },
        {
          title: "Compliance Reports",
          href: "/dashboard/accounting/reports/compliance",
          icon: <ChevronRight className="mr-2 h-4 w-4" />,
        },
      ],
    },
    {
      title: "Ledger",
      href: "/dashboard/accounting/ledger",
      icon: <BookOpen className="mr-2 h-4 w-4" />,
      submenu: [
        {
          title: "Chart of Accounts",
          href: "/dashboard/accounting/ledger/chart-of-accounts",
          icon: <ChevronRight className="mr-2 h-4 w-4" />,
        },
        {
          title: "General Ledger",
          href: "/dashboard/accounting/ledger/general-ledger",
          icon: <ChevronRight className="mr-2 h-4 w-4" />,
        },
        {
          title: "Journal Entries",
          href: "/dashboard/accounting/ledger/journal-entries",
          icon: <ChevronRight className="mr-2 h-4 w-4" />,
        },
        {
          title: "Trial Balance",
          href: "/dashboard/accounting/ledger/trial-balance",
          icon: <ChevronRight className="mr-2 h-4 w-4" />,
        },
      ],
    },
    {
      title: "Banking & Treasury",
      href: "/dashboard/accounting/banking",
      icon: <Landmark className="mr-2 h-4 w-4" />,
      submenu: [
        {
          title: "Accounts",
          href: "/dashboard/accounting/banking/accounts",
          icon: <ChevronRight className="mr-2 h-4 w-4" />,
        },
        {
          title: "Reconciliation",
          href: "/dashboard/accounting/banking/reconciliation",
          icon: <ChevronRight className="mr-2 h-4 w-4" />,
        },
        {
          title: "Cash Flow",
          href: "/dashboard/accounting/banking/cash-flow",
          icon: <ChevronRight className="mr-2 h-4 w-4" />,
        },
        {
          title: "Payments",
          href: "/dashboard/accounting/banking/payments",
          icon: <ChevronRight className="mr-2 h-4 w-4" />,
        },
      ],
    },
    {
      title: "Payment Management",
      href: "/dashboard/accounting/payments",
      icon: <CreditCard className="mr-2 h-4 w-4" />,
      submenu: [
        {
          title: "Payment Methods",
          href: "/dashboard/accounting/payments/methods",
          icon: <ChevronRight className="mr-2 h-4 w-4" />,
        },
        {
          title: "Payment Processing",
          href: "/dashboard/accounting/payments/processing",
          icon: <ChevronRight className="mr-2 h-4 w-4" />,
        },
        {
          title: "Payment Reports",
          href: "/dashboard/accounting/payments/reports",
          icon: <ChevronRight className="mr-2 h-4 w-4" />,
        },
      ],
    },
    {
      title: "Security Management",
      href: "/dashboard/accounting/security",
      icon: <Shield className="mr-2 h-4 w-4" />,
      submenu: [
        {
          title: "Access Control",
          href: "/dashboard/accounting/security/access-control",
          icon: <ChevronRight className="mr-2 h-4 w-4" />,
        },
        {
          title: "Audit Logs",
          href: "/dashboard/accounting/security/audit-logs",
          icon: <ChevronRight className="mr-2 h-4 w-4" />,
        },
        {
          title: "Security Settings",
          href: "/dashboard/accounting/security/settings",
          icon: <ChevronRight className="mr-2 h-4 w-4" />,
        },
      ],
    },
    {
      title: "Integrations",
      href: "/dashboard/accounting/integrations",
      icon: <LinkIcon className="mr-2 h-4 w-4" />,
      submenu: [
        {
          title: "Import/Export",
          href: "/dashboard/accounting/integrations/import-export",
          icon: <ChevronRight className="mr-2 h-4 w-4" />,
        },
      ],
    },
  ];

  return (
    <nav className="grid items-start gap-2">
      {navItems.map((item) => {
        const isActive = pathname === item.href || pathname.startsWith(item.href + "/");
        const hasSubmenu = item.submenu && item.submenu.length > 0;
        const isExpanded = expandedMenus.includes(item.href);

        return (
          <div key={item.href} className="flex flex-col">
            <div
              className={cn(
                "flex items-center gap-1 rounded-md px-3 py-2 text-sm font-medium hover:bg-accent hover:text-accent-foreground",
                isActive ? "bg-accent text-accent-foreground" : "transparent",
                hasSubmenu ? "cursor-pointer" : ""
              )}
              onClick={hasSubmenu ? () => toggleMenu(item.href) : undefined}
            >
              {hasSubmenu ? (
                <>
                  <div className="flex items-center flex-1">
                    {item.icon}
                    <span>{item.title}</span>
                  </div>
                  {isExpanded ? (
                    <ChevronDown className="h-4 w-4" />
                  ) : (
                    <ChevronRight className="h-4 w-4" />
                  )}
                </>
              ) : (
                <Link href={item.href} className="flex items-center flex-1">
                  {item.icon}
                  <span>{item.title}</span>
                </Link>
              )}
            </div>

            {hasSubmenu && isExpanded && (
              <div className="ml-4 mt-1 flex flex-col gap-1">
                {item.submenu?.map((subitem) => {
                  const isSubActive = pathname === subitem.href;

                  return (
                    <Link
                      key={subitem.href}
                      href={subitem.href}
                      className={cn(
                        "flex items-center gap-1 rounded-md px-3 py-2 text-sm font-medium hover:bg-accent hover:text-accent-foreground",
                        isSubActive ? "bg-accent/50 text-accent-foreground" : "transparent"
                      )}
                    >
                      {subitem.icon}
                      <span>{subitem.title}</span>
                    </Link>
                  );
                })}
              </div>
            )}
          </div>
        );
      })}
    </nav>
  );
}
