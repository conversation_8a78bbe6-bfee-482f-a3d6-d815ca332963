// lib/services/accounting/budget-transaction-service.ts
import mongoose from 'mongoose';
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import {
  Budget,
  BudgetCategory,
  BudgetSubcategory,
  IBudget,
  IBudgetCategory,
  IBudgetSubcategory
} from '@/models/accounting/Budget';
import Income from '@/models/accounting/Income';
import Expense from '@/models/accounting/Expense';
import BudgetExpenditure from '@/models/accounting/BudgetExpenditure';
import { budgetExpenditureIntegrationService } from './budget-expenditure-integration';

/**
 * Service for managing the integration between budgets and transactions
 */
export class BudgetTransactionService {
  /**
   * Get active budgets for transaction forms
   * @returns List of active budgets as plain objects
   */
  async getActiveBudgets(): Promise<any[]> {
    try {
      await connectToDatabase();
      logger.info('Getting active budgets', LogCategory.ACCOUNTING);

      const budgets = await Budget.find({
        status: { $in: ['approved', 'active'] },
        endDate: { $gte: new Date() }
      })
      .sort({ startDate: -1 })
      .select('id name fiscalYear startDate endDate totalIncome totalExpense totalActualIncome totalActualExpense')
      .lean();

      return budgets;
    } catch (error) {
      logger.error('Error getting active budgets', LogCategory.ACCOUNTING, error);
      throw error;
    }
  }

  /**
   * Get budget categories by type
   * @param budgetId Budget ID
   * @param type Category type ('income' or 'expense')
   * @returns List of budget categories as plain objects
   */
  async getBudgetCategories(budgetId: string, type: 'income' | 'expense'): Promise<any[]> {
    try {
      await connectToDatabase();
      logger.info('Getting budget categories', LogCategory.ACCOUNTING, { budgetId, type });

      const categories = await BudgetCategory.find({
        budget: new mongoose.Types.ObjectId(budgetId),
        type
      })
      .sort({ name: 1 })
      .select('id name description type budget total budgetedAmount actualAmount')
      .lean();

      return categories;
    } catch (error) {
      logger.error('Error getting budget categories', LogCategory.ACCOUNTING, error);
      throw error;
    }
  }

  /**
   * Get budget subcategories
   * @param categoryId Category ID
   * @returns List of subcategories as plain objects
   */
  async getBudgetSubcategories(categoryId: string): Promise<any[]> {
    try {
      await connectToDatabase();
      logger.info('Getting budget subcategories', LogCategory.ACCOUNTING, { categoryId });

      const subcategories = await BudgetSubcategory.find({
        parentCategory: new mongoose.Types.ObjectId(categoryId)
      })
      .sort({ name: 1 })
      .select('id name description parentCategory budget total')
      .lean();

      return subcategories;
    } catch (error) {
      logger.error('Error getting budget subcategories', LogCategory.ACCOUNTING, error);
      throw error;
    }
  }

  /**
   * Link income to budget and update budget actuals
   * @param incomeId Income ID
   * @param budgetId Budget ID
   * @param categoryId Category ID
   * @param subcategoryId Optional subcategory ID
   */
  async linkIncomeToBudget(
    incomeId: string,
    budgetId: string,
    categoryId: string,
    subcategoryId?: string
  ): Promise<void> {
    try {
      await connectToDatabase();
      logger.info('Linking income to budget', LogCategory.ACCOUNTING, {
        incomeId, budgetId, categoryId, subcategoryId
      });

      // Find the income transaction
      const income = await Income.findById(incomeId);
      if (!income) {
        throw new Error('Income not found');
      }

      // Find the budget and category
      const budget = await Budget.findById(budgetId);
      const category = await BudgetCategory.findById(categoryId);

      if (!budget || !category) {
        throw new Error('Budget or category not found');
      }

      // Verify category belongs to budget and is income type
      if (category.budget.toString() !== budgetId || category.type !== 'income') {
        throw new Error('Invalid category for this budget or type');
      }

      // Update income with budget references
      income.budget = new mongoose.Types.ObjectId(budgetId);
      income.budgetCategory = new mongoose.Types.ObjectId(categoryId);

      if (subcategoryId) {
        // Verify subcategory exists and belongs to this category
        const subcategory = await BudgetSubcategory.findById(subcategoryId);
        if (!subcategory) {
          throw new Error('Subcategory not found');
        }

        if (subcategory.parentCategory.toString() !== categoryId) {
          throw new Error('Subcategory does not belong to this category');
        }

        income.budgetSubcategory = new mongoose.Types.ObjectId(subcategoryId);
      }

      // Mark as applied to budget
      income.appliedToBudget = true;
      await income.save();

      // Update budget actual amounts
      await this.updateBudgetActuals(budgetId);

      // Update category actual amount
      await this.updateCategoryActual(categoryId);

      logger.info('Income linked to budget successfully', LogCategory.ACCOUNTING, { incomeId, budgetId });

      return;
    } catch (error) {
      logger.error('Error linking income to budget', LogCategory.ACCOUNTING, error);
      throw error;
    }
  }

  /**
   * Link expense to budget and update budget actuals
   * @param expenseId Expense ID
   * @param budgetId Budget ID
   * @param categoryId Category ID
   * @param subcategoryId Optional subcategory ID
   */
  async linkExpenseToBudget(
    expenseId: string,
    budgetId: string,
    categoryId: string,
    subcategoryId?: string
  ): Promise<void> {
    try {
      await connectToDatabase();
      logger.info('Linking expense to budget', LogCategory.ACCOUNTING, {
        expenseId, budgetId, categoryId, subcategoryId
      });

      // Find the expense transaction
      const expense = await Expense.findById(expenseId);
      if (!expense) {
        throw new Error('Expense not found');
      }

      // Find the budget and category
      const budget = await Budget.findById(budgetId);
      const category = await BudgetCategory.findById(categoryId);

      if (!budget || !category) {
        throw new Error('Budget or category not found');
      }

      // Verify category belongs to budget and is expense type
      if (category.budget.toString() !== budgetId || category.type !== 'expense') {
        throw new Error('Invalid category for this budget or type');
      }

      // Update expense with budget references
      expense.budget = new mongoose.Types.ObjectId(budgetId);
      expense.budgetCategory = new mongoose.Types.ObjectId(categoryId);

      if (subcategoryId) {
        // Verify subcategory exists and belongs to this category
        const subcategory = await BudgetSubcategory.findById(subcategoryId);
        if (!subcategory) {
          throw new Error('Subcategory not found');
        }

        if (subcategory.parentCategory.toString() !== categoryId) {
          throw new Error('Subcategory does not belong to this category');
        }

        expense.budgetSubcategory = new mongoose.Types.ObjectId(subcategoryId);
      }

      // Mark as applied to budget
      expense.appliedToBudget = true;
      await expense.save();

      // Update budget actual amounts
      await this.updateBudgetActuals(budgetId);

      // Update category actual amount
      await this.updateCategoryActual(categoryId);

      logger.info('Expense linked to budget successfully', LogCategory.ACCOUNTING, { expenseId, budgetId });

      return;
    } catch (error) {
      logger.error('Error linking expense to budget', LogCategory.ACCOUNTING, error);
      throw error;
    }
  }

  /**
   * Update budget actual amounts (Enhanced for expense types)
   * @param budgetId Budget ID
   * @param transactionType Optional transaction type for targeted updates
   * @returns Updated budget with actual amounts
   */
  async updateBudgetActuals(budgetId: string, transactionType?: 'income' | 'expense'): Promise<IBudget> {
    try {
      await connectToDatabase();
      logger.info('Updating budget actual amounts', LogCategory.ACCOUNTING, { budgetId, transactionType });

      const budget = await Budget.findById(budgetId);
      if (!budget) {
        throw new Error(`Budget with ID ${budgetId} not found`);
      }

      // Calculate total actual income (if not expense-only update)
      let totalActualIncome = budget.totalActualIncome || 0;
      if (!transactionType || transactionType === 'income') {
        const incomeResult = await Income.aggregate([
          {
            $match: {
              budget: new mongoose.Types.ObjectId(budgetId),
              status: 'received',
              appliedToBudget: true
            }
          },
          {
            $group: {
              _id: null,
              total: { $sum: '$amount' }
            }
          }
        ]);
        totalActualIncome = incomeResult.length > 0 ? incomeResult[0].total : 0;
      }

      // Calculate total actual expense (if not income-only update)
      let totalActualExpense = budget.totalActualExpense || 0;
      if (!transactionType || transactionType === 'expense') {
        const expenseResult = await Expense.aggregate([
          {
            $match: {
              budget: new mongoose.Types.ObjectId(budgetId),
              status: 'paid',
              appliedToBudget: true
            }
          },
          {
            $group: {
              _id: null,
              total: { $sum: '$amount' }
            }
          }
        ]);
        totalActualExpense = expenseResult.length > 0 ? expenseResult[0].total : 0;
      }

      // Update budget
      budget.totalActualIncome = totalActualIncome;
      budget.totalActualExpense = totalActualExpense;
      budget.lastActualUpdateDate = new Date();

      await budget.save();

      return budget;
    } catch (error) {
      logger.error('Error updating budget actuals', LogCategory.ACCOUNTING, error);
      throw error;
    }
  }

  /**
   * Update category actual amount (Enhanced for expense types)
   * @param categoryId Category ID
   * @param transactionType Optional transaction type for targeted updates
   * @returns Updated category with actual amount
   */
  async updateCategoryActual(categoryId: string, transactionType?: 'income' | 'expense'): Promise<IBudgetCategory> {
    try {
      await connectToDatabase();
      logger.info('Updating category actual amount', LogCategory.ACCOUNTING, { categoryId });

      const category = await BudgetCategory.findById(categoryId);
      if (!category) {
        throw new Error(`Category with ID ${categoryId} not found`);
      }

      if (category.type === 'income') {
        // Calculate actual income for this category
        const incomeResult = await Income.aggregate([
          {
            $match: {
              budgetCategory: new mongoose.Types.ObjectId(categoryId),
              status: 'received',
              appliedToBudget: true
            }
          },
          {
            $group: {
              _id: null,
              total: { $sum: '$amount' }
            }
          }
        ]);

        category.actualAmount = incomeResult.length > 0 ? incomeResult[0].total : 0;
      } else {
        // Calculate actual expense for this category
        const expenseResult = await Expense.aggregate([
          {
            $match: {
              budgetCategory: new mongoose.Types.ObjectId(categoryId),
              status: 'paid',
              appliedToBudget: true
            }
          },
          {
            $group: {
              _id: null,
              total: { $sum: '$amount' }
            }
          }
        ]);

        category.actualAmount = expenseResult.length > 0 ? expenseResult[0].total : 0;
      }

      category.lastActualUpdateDate = new Date();
      await category.save();

      return category;
    } catch (error) {
      logger.error('Error updating category actual amount', LogCategory.ACCOUNTING, error);
      throw error;
    }
  }

  /**
   * Update income in budget when income is modified
   * @param incomeId Income ID
   * @param budgetId Budget ID
   * @param categoryId Category ID
   * @param subcategoryId Optional subcategory ID
   */
  async updateIncomeInBudget(
    incomeId: string,
    budgetId: string,
    categoryId: string,
    subcategoryId?: string
  ): Promise<void> {
    try {
      await connectToDatabase();
      logger.info('Updating income in budget', LogCategory.ACCOUNTING, {
        incomeId, budgetId, categoryId, subcategoryId
      });

      // Update budget actual amounts
      await this.updateBudgetActuals(budgetId);

      // Update category actual amount
      await this.updateCategoryActual(categoryId);

      // Update subcategory if provided
      if (subcategoryId) {
        await this.updateSubcategoryActual(subcategoryId);
      }

      logger.info('Income updated in budget successfully', LogCategory.ACCOUNTING, { incomeId, budgetId });
    } catch (error) {
      logger.error('Error updating income in budget', LogCategory.ACCOUNTING, error);
      throw error;
    }
  }

  /**
   * Remove income from budget when income is deleted
   * @param incomeId Income ID
   * @param budgetId Budget ID
   * @param categoryId Category ID
   * @param subcategoryId Optional subcategory ID
   */
  async removeIncomeFromBudget(
    incomeId: string,
    budgetId: string,
    categoryId: string,
    subcategoryId?: string
  ): Promise<void> {
    try {
      await connectToDatabase();
      logger.info('Removing income from budget', LogCategory.ACCOUNTING, {
        incomeId, budgetId, categoryId, subcategoryId
      });

      // Update budget actual amounts
      await this.updateBudgetActuals(budgetId);

      // Update category actual amount
      await this.updateCategoryActual(categoryId);

      // Update subcategory if provided
      if (subcategoryId) {
        await this.updateSubcategoryActual(subcategoryId);
      }

      logger.info('Income removed from budget successfully', LogCategory.ACCOUNTING, { incomeId, budgetId });
    } catch (error) {
      logger.error('Error removing income from budget', LogCategory.ACCOUNTING, error);
      throw error;
    }
  }

  /**
   * Update subcategory actual amount
   * @param subcategoryId Subcategory ID
   * @returns Updated subcategory with actual amount
   */
  async updateSubcategoryActual(subcategoryId: string): Promise<IBudgetSubcategory> {
    try {
      await connectToDatabase();
      logger.info('Updating subcategory actual amount', LogCategory.ACCOUNTING, { subcategoryId });

      const subcategory = await BudgetSubcategory.findById(subcategoryId);
      if (!subcategory) {
        throw new Error(`Subcategory with ID ${subcategoryId} not found`);
      }

      // Get parent category to determine type
      const category = await BudgetCategory.findById(subcategory.parentCategory);
      if (!category) {
        throw new Error('Parent category not found');
      }

      if (category.type === 'income') {
        // Calculate actual income for this subcategory
        const incomeResult = await Income.aggregate([
          {
            $match: {
              budgetSubcategory: new mongoose.Types.ObjectId(subcategoryId),
              status: 'received',
              appliedToBudget: true
            }
          },
          {
            $group: {
              _id: null,
              total: { $sum: '$amount' }
            }
          }
        ]);

        subcategory.actualAmount = incomeResult.length > 0 ? incomeResult[0].total : 0;
      } else {
        // Calculate actual expense for this subcategory
        const expenseResult = await Expense.aggregate([
          {
            $match: {
              budgetSubcategory: new mongoose.Types.ObjectId(subcategoryId),
              status: 'paid',
              appliedToBudget: true
            }
          },
          {
            $group: {
              _id: null,
              total: { $sum: '$amount' }
            }
          }
        ]);

        subcategory.actualAmount = expenseResult.length > 0 ? expenseResult[0].total : 0;
      }

      subcategory.lastActualUpdateDate = new Date();
      await subcategory.save();

      return subcategory;
    } catch (error) {
      logger.error('Error updating subcategory actual amount', LogCategory.ACCOUNTING, error);
      throw error;
    }
  }

  /**
   * Update expense in budget when expense is modified (Enhanced with BudgetExpenditure)
   * @param expenseId Expense ID
   * @param budgetId Budget ID
   * @param categoryId Category ID
   * @param subcategoryId Optional subcategory ID
   */
  async updateExpenseInBudget(
    expenseId: string,
    budgetId: string,
    categoryId: string,
    subcategoryId?: string
  ): Promise<void> {
    try {
      await connectToDatabase();
      logger.info('Updating expense in budget', LogCategory.ACCOUNTING, {
        expenseId, budgetId, categoryId, subcategoryId
      });

      // Get the expense to check if it uses BudgetExpenditure integration
      const expense = await Expense.findById(expenseId);
      if (expense && expense.appliedToBudget && expense.budget && expense.budgetCategory) {
        // Use BudgetExpenditure integration service
        await budgetExpenditureIntegrationService.updateExpenditureAsBudgetItem(expense);
      }

      // Update budget actual amounts
      await this.updateBudgetActuals(budgetId, 'expense');

      // Update category actual amount
      await this.updateCategoryActual(categoryId, 'expense');

      // Update subcategory if provided
      if (subcategoryId) {
        await this.updateSubcategoryActual(subcategoryId);
      }

      logger.info('Expense updated in budget successfully', LogCategory.ACCOUNTING, { expenseId, budgetId });
    } catch (error) {
      logger.error('Error updating expense in budget', LogCategory.ACCOUNTING, error);
      throw error;
    }
  }

  /**
   * Remove expense from budget when expense is deleted (Enhanced with BudgetExpenditure)
   * @param expenseId Expense ID
   * @param budgetId Budget ID
   * @param categoryId Category ID
   * @param subcategoryId Optional subcategory ID
   */
  async removeExpenseFromBudget(
    expenseId: string,
    budgetId: string,
    categoryId: string,
    subcategoryId?: string
  ): Promise<void> {
    try {
      await connectToDatabase();
      logger.info('Removing expense from budget', LogCategory.ACCOUNTING, {
        expenseId, budgetId, categoryId, subcategoryId
      });

      // Remove from BudgetExpenditure integration
      await budgetExpenditureIntegrationService.removeExpenditureFromBudget(expenseId);

      // Update budget actual amounts
      await this.updateBudgetActuals(budgetId, 'expense');

      // Update category actual amount
      await this.updateCategoryActual(categoryId, 'expense');

      // Update subcategory if provided
      if (subcategoryId) {
        await this.updateSubcategoryActual(subcategoryId);
      }

      logger.info('Expense removed from budget successfully', LogCategory.ACCOUNTING, { expenseId, budgetId });
    } catch (error) {
      logger.error('Error removing expense from budget', LogCategory.ACCOUNTING, error);
      throw error;
    }
  }

  /**
   * Handle expense status change with budget impact (NEW)
   * @param expenseId Expense ID
   * @param oldStatus Previous status
   * @param newStatus New status
   */
  async handleExpenseStatusChange(
    expenseId: string,
    oldStatus: string,
    newStatus: string
  ): Promise<void> {
    try {
      await connectToDatabase();
      logger.info('Handling expense status change for budget', LogCategory.ACCOUNTING, {
        expenseId, oldStatus, newStatus
      });

      // Get the expense
      const expense = await Expense.findById(expenseId);
      if (!expense) {
        throw new Error('Expense not found');
      }

      // Use BudgetExpenditure integration for status changes
      if (expense.appliedToBudget && expense.budget && expense.budgetCategory) {
        await budgetExpenditureIntegrationService.handleExpenditureStatusChange(expense, oldStatus);

        // Update budget actuals if status changed to/from paid
        if ((newStatus === 'paid' && oldStatus !== 'paid') || (oldStatus === 'paid' && newStatus !== 'paid')) {
          await this.updateBudgetActuals(expense.budget.toString(), 'expense');
          await this.updateCategoryActual(expense.budgetCategory.toString(), 'expense');
        }
      }

      logger.info('Expense status change handled successfully', LogCategory.ACCOUNTING, { expenseId, oldStatus, newStatus });
    } catch (error) {
      logger.error('Error handling expense status change', LogCategory.ACCOUNTING, error);
      throw error;
    }
  }

  /**
   * Get budget expenditure summary (NEW)
   * @param budgetId Budget ID
   * @returns Budget expenditure summary
   */
  async getBudgetExpenditureSummary(budgetId: string): Promise<any> {
    try {
      await connectToDatabase();
      logger.info('Getting budget expenditure summary', LogCategory.ACCOUNTING, { budgetId });

      // Get BudgetExpenditure records for this budget
      const expenditures = await BudgetExpenditure.find({
        budget: new mongoose.Types.ObjectId(budgetId)
      })
        .populate('budgetCategory', 'name type budgetedAmount')
        .populate('sourceExpenditure', 'reference description category')
        .sort({ date: -1 })
        .lean();

      // Group by status
      const statusSummary = expenditures.reduce((acc, exp) => {
        if (!acc[exp.status]) {
          acc[exp.status] = { count: 0, total: 0 };
        }
        acc[exp.status].count++;
        acc[exp.status].total += exp.amountInBaseCurrency;
        return acc;
      }, {} as Record<string, { count: number; total: number }>);

      // Group by category
      const categorySummary = expenditures.reduce((acc, exp) => {
        const categoryId = exp.budgetCategory._id.toString();
        if (!acc[categoryId]) {
          acc[categoryId] = {
            categoryName: exp.budgetCategory.name,
            budgetedAmount: exp.budgetCategory.budgetedAmount || 0,
            count: 0,
            total: 0,
            utilization: 0
          };
        }
        acc[categoryId].count++;
        acc[categoryId].total += exp.amountInBaseCurrency;
        acc[categoryId].utilization = acc[categoryId].budgetedAmount > 0 ?
          (acc[categoryId].total / acc[categoryId].budgetedAmount) * 100 : 0;
        return acc;
      }, {} as Record<string, any>);

      // Calculate totals
      const totalExpenditure = expenditures.reduce((sum, exp) => sum + exp.amountInBaseCurrency, 0);
      const totalApproved = expenditures
        .filter(exp => exp.status === 'approved')
        .reduce((sum, exp) => sum + exp.amountInBaseCurrency, 0);
      const totalPaid = expenditures
        .filter(exp => exp.status === 'paid')
        .reduce((sum, exp) => sum + exp.amountInBaseCurrency, 0);

      return {
        budgetId,
        totalExpenditure,
        totalApproved,
        totalPaid,
        expenditureCount: expenditures.length,
        statusSummary,
        categorySummary: Object.values(categorySummary),
        recentExpenditures: expenditures.slice(0, 10) // Last 10 expenditures
      };
    } catch (error) {
      logger.error('Error getting budget expenditure summary', LogCategory.ACCOUNTING, error);
      throw error;
    }
  }
}

// Export a singleton instance
export const budgetTransactionService = new BudgetTransactionService();
