// lib/services/accounting/budget-integration-service.ts
import mongoose from 'mongoose';
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { Budget, BudgetCategory, BudgetSubcategory, BudgetItem } from '@/models/accounting/Budget';
import { Transaction } from '@/models/accounting/Transaction';
import Income from '@/models/accounting/Income';
import Expense from '@/models/accounting/Expense';
import BudgetExpenditure from '@/models/accounting/BudgetExpenditure';
import { notificationService } from '@/lib/backend/services/notification/NotificationService';
import budgetService from './budget-service';
import { budgetTransactionService } from './budget-transaction-service';
import { budgetExpenditureIntegrationService } from './budget-expenditure-integration';

/**
 * Service for integrating budgets with other modules
 */
export class BudgetIntegrationService {
  /**
   * Link a transaction to a budget category
   * @param transactionId - Transaction ID
   * @param budgetId - Budget ID
   * @param categoryId - Budget category ID
   * @param itemId - Budget item ID (optional)
   * @returns Updated transaction
   */
  async linkTransactionToBudget(
    transactionId: string,
    budgetId: string,
    categoryId: string,
    itemId?: string
  ): Promise<any> {
    try {
      await connectToDatabase();
      logger.info('Linking transaction to budget', LogCategory.ACCOUNTING, {
        transactionId,
        budgetId,
        categoryId,
        itemId
      });

      // Get transaction
      const transaction = await Transaction.findById(transactionId);
      if (!transaction) {
        throw new Error(`Transaction with ID ${transactionId} not found`);
      }

      // Get budget
      const budget = await Budget.findById(budgetId);
      if (!budget) {
        throw new Error(`Budget with ID ${budgetId} not found`);
      }

      // Get category
      const category = await BudgetCategory.findById(categoryId);
      if (!category) {
        throw new Error(`Budget category with ID ${categoryId} not found`);
      }

      // Verify category belongs to budget
      if (category.budget.toString() !== budgetId) {
        throw new Error(`Category does not belong to the specified budget`);
      }

      // Get item if provided
      let item = null;
      if (itemId) {
        item = await BudgetItem.findById(itemId);
        if (!item) {
          throw new Error(`Budget item with ID ${itemId} not found`);
        }

        // Verify item belongs to category
        if (item.parentCategory.toString() !== categoryId) {
          throw new Error(`Item does not belong to the specified category`);
        }
      }

      // Update transaction metadata
      transaction.metadata = {
        ...transaction.metadata,
        budgetId,
        categoryId,
        categoryName: category.name,
        categoryType: category.type,
        itemId: item ? item._id : undefined,
        itemName: item ? item.name : undefined
      };

      // Save transaction
      await transaction.save();

      // Update budget actuals
      await this.updateBudgetActuals(budgetId);

      return transaction;
    } catch (error) {
      logger.error('Error linking transaction to budget', LogCategory.ACCOUNTING, error);
      throw error;
    }
  }

  /**
   * Update budget actual amounts based on linked transactions
   * @param budgetId - Budget ID
   * @returns Updated budget
   */
  async updateBudgetActuals(budgetId: string): Promise<any> {
    try {
      await connectToDatabase();
      logger.info('Updating budget actuals', LogCategory.ACCOUNTING, { budgetId });

      // Get budget
      const budget = await Budget.findById(budgetId);
      if (!budget) {
        throw new Error(`Budget with ID ${budgetId} not found`);
      }

      // Get transactions linked to this budget
      const transactions = await Transaction.find({
        'metadata.budgetId': budgetId,
        date: { $gte: budget.startDate, $lte: budget.endDate }
      });

      // Get categories
      const categories = await BudgetCategory.find({ budget: budgetId });

      // Calculate actual amounts for each category
      let totalActualIncome = 0;
      let totalActualExpense = 0;

      for (const category of categories) {
        // Get transactions for this category
        const categoryTransactions = transactions.filter(
          transaction => transaction.metadata?.categoryId === category._id.toString()
        );

        // Calculate actual amount
        const actualAmount = categoryTransactions.reduce((total, transaction) => {
          return total + transaction.amount;
        }, 0);

        // Update category actual amount (if we had this field)
        // For now, we'll just calculate totals

        // Add to budget totals
        if (category.type === 'income') {
          totalActualIncome += actualAmount;
        } else {
          totalActualExpense += actualAmount;
        }
      }

      // Update budget
      budget.totalActualIncome = totalActualIncome;
      budget.totalActualExpense = totalActualExpense;
      budget.lastActualUpdateDate = new Date();
      await budget.save();

      return budget;
    } catch (error) {
      logger.error('Error updating budget actuals', LogCategory.ACCOUNTING, error);
      throw error;
    }
  }

  /**
   * Handle new income transaction
   * @param income - Income document
   * @returns Updated income
   */
  async handleNewIncome(income: any): Promise<any> {
    try {
      await connectToDatabase();
      logger.info('Handling new income for budget integration', LogCategory.ACCOUNTING, {
        incomeId: income._id
      });

      // Find active budget for this date
      const activeBudget = await Budget.findOne({
        startDate: { $lte: income.date },
        endDate: { $gte: income.date },
        status: { $in: ['approved', 'active'] }
      });

      if (!activeBudget) {
        logger.info('No active budget found for income date', LogCategory.ACCOUNTING, {
          incomeId: income._id,
          date: income.date
        });
        return income;
      }

      // Find matching category based on income category
      const matchingCategory = await BudgetCategory.findOne({
        budget: activeBudget._id,
        type: 'income',
        name: { $regex: new RegExp(income.category, 'i') }
      });

      if (!matchingCategory) {
        logger.info('No matching budget category found for income', LogCategory.ACCOUNTING, {
          incomeId: income._id,
          category: income.category
        });
        return income;
      }

      // Create transaction if it doesn't exist
      let transaction = await Transaction.findOne({ sourceId: income._id, sourceType: 'Income' });

      if (!transaction) {
        transaction = await Transaction.create({
          date: income.date,
          amount: income.amount,
          description: income.description,
          type: 'income',
          sourceId: income._id,
          sourceType: 'Income',
          metadata: {
            budgetId: activeBudget._id,
            categoryId: matchingCategory._id,
            categoryName: matchingCategory.name,
            categoryType: 'income'
          }
        });
      }

      // Update budget actuals
      await this.updateBudgetActuals(activeBudget._id.toString());

      return income;
    } catch (error) {
      logger.error('Error handling new income for budget integration', LogCategory.ACCOUNTING, error);
      // Don't throw error, just log it
      return income;
    }
  }

  /**
   * Handle new expense transaction
   * @param expense - Expense document
   * @returns Updated expense
   */
  async handleNewExpense(expense: any): Promise<any> {
    try {
      await connectToDatabase();
      logger.info('Handling new expense for budget integration', LogCategory.ACCOUNTING, {
        expenseId: expense._id,
        status: expense.status,
        appliedToBudget: expense.appliedToBudget
      });

      // Enhanced: Use BudgetExpenditure integration if expense has budget linkage
      if (expense.appliedToBudget && expense.budget && expense.budgetCategory) {
        // Use the new BudgetExpenditure integration service
        await budgetExpenditureIntegrationService.createExpenditureAsBudgetItem(expense);

        logger.info('Expense processed through BudgetExpenditure integration', LogCategory.ACCOUNTING, {
          expenseId: expense._id,
          budgetId: expense.budget,
          categoryId: expense.budgetCategory
        });

        return expense;
      }

      // Fallback: Use traditional budget integration for expenses without direct budget linkage
      const activeBudget = await Budget.findOne({
        startDate: { $lte: expense.date },
        endDate: { $gte: expense.date },
        status: { $in: ['approved', 'active'] }
      });

      if (!activeBudget) {
        logger.info('No active budget found for expense date', LogCategory.ACCOUNTING, {
          expenseId: expense._id,
          date: expense.date
        });
        return expense;
      }

      // Find matching category based on expense category
      const matchingCategory = await BudgetCategory.findOne({
        budget: activeBudget._id,
        type: 'expense',
        name: { $regex: new RegExp(expense.category, 'i') }
      });

      if (!matchingCategory) {
        logger.info('No matching budget category found for expense', LogCategory.ACCOUNTING, {
          expenseId: expense._id,
          category: expense.category
        });
        return expense;
      }

      // Create transaction if it doesn't exist
      let transaction = await Transaction.findOne({ sourceId: expense._id, sourceType: 'Expense' });

      if (!transaction) {
        transaction = await Transaction.create({
          date: expense.date,
          amount: expense.amount,
          description: expense.description,
          type: 'expense',
          sourceId: expense._id,
          sourceType: 'Expense',
          metadata: {
            budgetId: activeBudget._id,
            categoryId: matchingCategory._id,
            categoryName: matchingCategory.name,
            categoryType: 'expense'
          }
        });
      }

      // Update budget actuals
      await this.updateBudgetActuals(activeBudget._id.toString());

      return expense;
    } catch (error) {
      logger.error('Error handling new expense for budget integration', LogCategory.ACCOUNTING, error);
      // Don't throw error, just log it
      return expense;
    }
  }

  /**
   * Handle expense status changes (NEW - Enhanced workflow)
   * @param expense - Expense document
   * @param oldStatus - Previous status
   * @returns Updated expense
   */
  async handleExpenseStatusChange(expense: any, oldStatus: string): Promise<any> {
    try {
      await connectToDatabase();
      logger.info('Handling expense status change for budget integration', LogCategory.ACCOUNTING, {
        expenseId: expense._id,
        oldStatus,
        newStatus: expense.status,
        appliedToBudget: expense.appliedToBudget
      });

      // Use BudgetExpenditure integration for status changes
      if (expense.appliedToBudget && expense.budget && expense.budgetCategory) {
        await budgetExpenditureIntegrationService.handleExpenditureStatusChange(expense, oldStatus);

        // Also update traditional budget actuals for paid expenses
        if (expense.status === 'paid' && oldStatus !== 'paid') {
          await this.updateBudgetActuals(expense.budget.toString());
        }

        logger.info('Expense status change processed through BudgetExpenditure integration', LogCategory.ACCOUNTING, {
          expenseId: expense._id,
          oldStatus,
          newStatus: expense.status
        });
      }

      return expense;
    } catch (error) {
      logger.error('Error handling expense status change for budget integration', LogCategory.ACCOUNTING, error);
      return expense;
    }
  }

  /**
   * Update budget for expense (Enhanced with BudgetExpenditure support)
   * @param expense - Expense document
   * @param operation - Operation type
   * @returns Updated expense
   */
  async updateBudgetForExpense(expense: any, operation: 'add' | 'remove' | 'update'): Promise<any> {
    try {
      await connectToDatabase();
      logger.info('Updating budget for expense', LogCategory.ACCOUNTING, {
        expenseId: expense._id,
        operation,
        appliedToBudget: expense.appliedToBudget
      });

      // Use BudgetExpenditure integration for budget-linked expenses
      if (expense.appliedToBudget && expense.budget && expense.budgetCategory) {
        switch (operation) {
          case 'add':
            await budgetExpenditureIntegrationService.createExpenditureAsBudgetItem(expense);
            break;
          case 'update':
            await budgetExpenditureIntegrationService.updateExpenditureAsBudgetItem(expense);
            break;
          case 'remove':
            await budgetExpenditureIntegrationService.removeExpenditureFromBudget(expense._id.toString());
            break;
        }

        // Update budget actuals for paid expenses
        if (expense.status === 'paid') {
          await this.updateBudgetActuals(expense.budget.toString());
        }
      }

      return expense;
    } catch (error) {
      logger.error('Error updating budget for expense', LogCategory.ACCOUNTING, error);
      return expense;
    }
  }

  /**
   * Get budget performance for a specific period
   * @param budgetId - Budget ID
   * @param period - Period (month, quarter, year)
   * @returns Budget performance data
   */
  async getBudgetPerformance(
    budgetId: string,
    period?: {
      month?: number;
      quarter?: number;
      year?: number;
    }
  ): Promise<any> {
    try {
      await connectToDatabase();
      logger.info('Getting budget performance', LogCategory.ACCOUNTING, { budgetId, period });

      // Get budget with details
      const budget = await budgetService.getBudgetWithDetails(budgetId);
      if (!budget) {
        throw new Error(`Budget with ID ${budgetId} not found`);
      }

      // Get income categories
      const incomeCategories = await BudgetCategory.find({
        budget: budgetId,
        type: 'income'
      }).lean();

      // Get expense categories
      const expenseCategories = await BudgetCategory.find({
        budget: budgetId,
        type: 'expense'
      }).lean();

      // Calculate total budgeted income and expense
      const totalBudgetedIncome = incomeCategories.reduce((sum, category) => sum + (category.budgetedAmount || 0), 0);
      const totalBudgetedExpense = expenseCategories.reduce((sum, category) => sum + (category.budgetedAmount || 0), 0);

      // Build date filter based on period
      const dateFilter: any = {
        $gte: budget.startDate,
        $lte: budget.endDate
      };

      if (period) {
        if (period.month) {
          const year = period.year || new Date().getFullYear();
          const startDate = new Date(year, period.month - 1, 1);
          const endDate = new Date(year, period.month, 0);
          dateFilter.$gte = startDate;
          dateFilter.$lte = endDate;
        } else if (period.quarter) {
          const year = period.year || new Date().getFullYear();
          const startMonth = (period.quarter - 1) * 3;
          const startDate = new Date(year, startMonth, 1);
          const endDate = new Date(year, startMonth + 3, 0);
          dateFilter.$gte = startDate;
          dateFilter.$lte = endDate;
        } else if (period.year) {
          const startDate = new Date(period.year, 0, 1);
          const endDate = new Date(period.year, 11, 31);
          dateFilter.$gte = startDate;
          dateFilter.$lte = endDate;
        }
      }

      // Get actual income with date filter
      const incomeResult = await Income.aggregate([
        {
          $match: {
            budget: new mongoose.Types.ObjectId(budgetId),
            status: 'received',
            appliedToBudget: true,
            date: dateFilter
          }
        },
        {
          $group: {
            _id: '$budgetCategory',
            total: { $sum: '$amount' }
          }
        }
      ]);

      // Get actual expense with date filter
      const expenseResult = await Expense.aggregate([
        {
          $match: {
            budget: new mongoose.Types.ObjectId(budgetId),
            status: 'paid',
            appliedToBudget: true,
            date: dateFilter
          }
        },
        {
          $group: {
            _id: '$budgetCategory',
            total: { $sum: '$amount' }
          }
        }
      ]);

      // Create a map of category ID to actual amount
      const incomeCategoryMap = new Map();
      incomeResult.forEach(item => {
        incomeCategoryMap.set(item._id.toString(), item.total);
      });

      const expenseCategoryMap = new Map();
      expenseResult.forEach(item => {
        expenseCategoryMap.set(item._id.toString(), item.total);
      });

      // Calculate total actual income and expense
      const totalActualIncome = incomeResult.reduce((sum, item) => sum + item.total, 0);
      const totalActualExpense = expenseResult.reduce((sum, item) => sum + item.total, 0);

      // Format income categories
      const formattedIncomeCategories = incomeCategories.map(category => {
        const actual = incomeCategoryMap.get(category._id.toString()) || 0;
        const budgeted = category.budgetedAmount || 0;
        const variance = actual - budgeted;
        const percentage = budgeted > 0 ? (actual / budgeted) * 100 : 0;

        return {
          id: category._id.toString(),
          name: category.name,
          budgeted,
          actual,
          variance,
          percentage
        };
      });

      // Format expense categories
      const formattedExpenseCategories = expenseCategories.map(category => {
        const actual = expenseCategoryMap.get(category._id.toString()) || 0;
        const budgeted = category.budgetedAmount || 0;
        const variance = actual - budgeted;
        const percentage = budgeted > 0 ? (actual / budgeted) * 100 : 0;

        return {
          id: category._id.toString(),
          name: category.name,
          budgeted,
          actual,
          variance,
          percentage
        };
      });

      // Calculate variances
      const incomeVariance = totalActualIncome - totalBudgetedIncome;
      const expenseVariance = totalActualExpense - totalBudgetedExpense;

      // Return performance data
      return {
        budgetId: budget._id,
        name: budget.name,
        period: period || { year: new Date(budget.startDate).getFullYear() },
        budgetedIncome: totalBudgetedIncome,
        budgetedExpense: totalBudgetedExpense,
        actualIncome: totalActualIncome,
        actualExpense: totalActualExpense,
        incomeVariance,
        expenseVariance,
        incomeCategories: formattedIncomeCategories,
        expenseCategories: formattedExpenseCategories
      };
    } catch (error) {
      logger.error('Error getting budget performance', LogCategory.ACCOUNTING, error);
      throw error;
    }
  }
}

// Export the service
export default new BudgetIntegrationService();
