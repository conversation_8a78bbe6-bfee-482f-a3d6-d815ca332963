'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { DashboardShell } from "@/components/dashboard-shell";
import { Button } from "@/components/ui/button";
import { ArrowLeft, X } from "lucide-react";
import Link from "next/link";
import { useToast } from "@/hooks/use-toast";
import { ProgressiveExpenditureForm } from "@/components/accounting/expenditure/progressive-expenditure-form";
import { useExpenditureStore } from "@/lib/stores/expenditure-store";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

export default function NewExpenditurePage() {
  const router = useRouter();
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const {
    createExpenditure,
    initializeFormData,
    isFormDataReady,
    error,
    setError
  } = useExpenditureStore();

  // Initialize form data on mount
  React.useEffect(() => {
    if (!isFormDataReady) {
      initializeFormData();
    }
  }, [isFormDataReady, initializeFormData]);

  // Handle form submission using the store
  const handleSubmit = async (data: any) => {
    try {
      console.log('Expenditure form submission started:', data);
      setIsLoading(true);

      // Use the store's createExpenditure method for enhanced integration
      await createExpenditure(data);

      toast({
        title: "Success",
        description: "Expenditure record created successfully with budget integration.",
      });

      // Navigate back to overview page
      router.push('/dashboard/accounting/expenditure/overview');

    } catch (error) {
      console.error('Error creating expenditure:', error);
      const errorMessage = error instanceof Error ? error.message : "Failed to create expenditure record.";

      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Handle form cancellation
  const handleCancel = () => {
    router.push('/dashboard/accounting/expenditure/overview');
  };

  return (
    <DashboardShell>
      <div className="flex flex-col gap-8">
        {/* Page Header */}
        <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
          <div>
            <div className="flex items-center gap-2 mb-2">
              <Button variant="ghost" size="sm" asChild>
                <Link href="/dashboard/accounting/expenditure/overview">
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Back to Expenditure Overview
                </Link>
              </Button>
            </div>
            <h1 className="text-3xl font-bold tracking-tight">Create New Expenditure Record</h1>
            <p className="text-muted-foreground">
              Record a new expenditure transaction for the Teachers Council of Malawi
            </p>
          </div>
          <div className="flex items-center gap-2">
            <Badge variant="outline" className="text-xs">
              Progressive Loading
            </Badge>
            <Badge variant="default" className="text-xs">
              Enhanced Integration
            </Badge>
          </div>
        </div>

        {/* Form Container */}
        <div className="max-w-4xl mx-auto w-full">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span>Expenditure Transaction Details</span>
                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleCancel}
                    disabled={isLoading}
                  >
                    <X className="h-4 w-4 mr-2" />
                    Cancel
                  </Button>
                </div>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ProgressiveExpenditureForm
                onSubmit={handleSubmit}
                onCancel={handleCancel}
                isLoading={isLoading}
              />
            </CardContent>
          </Card>
        </div>

        {/* Help Section */}
        <div className="max-w-4xl mx-auto w-full">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Form Guidelines</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div>
                  <h4 className="font-medium mb-2">Required Fields</h4>
                  <ul className="space-y-1 text-muted-foreground">
                    <li>• Date of expenditure transaction</li>
                    <li>• Expenditure category (operational, capital, etc.)</li>
                    <li>• Amount in Malawi Kwacha (MWK)</li>
                    <li>• Reference number or identifier</li>
                    <li>• Fiscal year (defaults to 2025-2026)</li>
                    <li>• Budget allocation</li>
                    <li>• Budget category</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-medium mb-2">Optional Fields</h4>
                  <ul className="space-y-1 text-muted-foreground">
                    <li>• Vendor information</li>
                    <li>• Department or cost center</li>
                    <li>• Payment method</li>
                    <li>• Description or notes</li>
                    <li>• Status (defaults to draft)</li>
                  </ul>
                </div>
              </div>
              
              <div className="mt-4 p-4 bg-blue-50 rounded-lg">
                <h4 className="font-medium mb-2 text-blue-900">Enhanced Budget Integration</h4>
                <p className="text-sm text-blue-800">
                  This expenditure form uses progressive loading for optimal performance and enhanced
                  budget integration. It automatically creates BudgetExpenditure records, updates
                  budget actuals, and provides real-time variance tracking across multiple databases.
                  The default fiscal year is set to 2025-2026.
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </DashboardShell>
  );
}
